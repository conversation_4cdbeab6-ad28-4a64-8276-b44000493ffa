﻿/**
 * Workflow Canvas Component
 * Manages the main canvas area where nodes are displayed and manipulated
 */

class WorkflowCanvas {
  constructor(element, eventEmitter) {
    this.element = element;
    this.eventEmitter = eventEmitter;
    this.svg = element.querySelector('#canvas-svg');
    this.nodesOverlay = element.querySelector('#nodes-overlay');

    this.zoom = 1;
    this.pan = { x: 0, y: 0 };
    this.gridSize = 20;
    this.snapToGrid = true;
    this.showGridLines = true;

    this.isDragging = false;
    this.dragStart = { x: 0, y: 0 };

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.updateTransform();
    this.updateGrid();
  }

  setupEventListeners() {
    // Canvas panning
    this.element.addEventListener('mousedown', (e) => {
      if (e.target === this.element || e.target === this.svg) {
        this.startPan(e);
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (this.isDragging) {
        this.updatePan(e);
      }
    });

    document.addEventListener('mouseup', () => {
      this.endPan();
    });

    // Zoom with mouse wheel
    this.element.addEventListener('wheel', (e) => {
      e.preventDefault();
      const delta = e.deltaY > 0 ? 0.9 : 1.1;
      this.zoomAt(e.clientX, e.clientY, delta);
    });

    // Canvas click (deselect nodes)
    this.element.addEventListener('click', (e) => {
      if (e.target === this.element || e.target === this.svg) {
        this.eventEmitter.emit('canvas:clicked');
        this.eventEmitter.emit('node:deselect:all');
      }
    });

    // Drop zone for new nodes
    this.element.addEventListener('dragover', (e) => {
      e.preventDefault();
      this.element.classList.add('drag-over');
    });

    this.element.addEventListener('dragleave', (e) => {
      if (!this.element.contains(e.relatedTarget)) {
        this.element.classList.remove('drag-over');
      }
    });

    this.element.addEventListener('drop', (e) => {
      e.preventDefault();
      this.element.classList.remove('drag-over');

      const nodeType = e.dataTransfer.getData('text/node-type');
      if (nodeType) {
        const canvasRect = this.element.getBoundingClientRect();
        const position = this.screenToCanvas({
          x: e.clientX - canvasRect.left,
          y: e.clientY - canvasRect.top
        });

        this.eventEmitter.emit('node:drop', {
          nodeType,
          position: this.snapToGrid ? this.snapPosition(position) : position
        });
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

      switch (e.key) {
        case '+':
        case '=':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            this.zoomIn();
          }
          break;
        case '-':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            this.zoomOut();
          }
          break;
        case '0':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            this.resetZoom();
          }
          break;
      }
    });
  }

  startPan(e) {
    // Don't start panning if clicking on a node
    if (e.target.closest('.workflow-node')) {
      return;
    }

    this.isDragging = true;
    this.dragStart = { x: e.clientX - this.pan.x, y: e.clientY - this.pan.y };
    this.element.style.cursor = 'grabbing';
  }

  updatePan(e) {
    if (!this.isDragging) return;

    this.pan.x = e.clientX - this.dragStart.x;
    this.pan.y = e.clientY - this.dragStart.y;
    this.updateTransform();
  }

  endPan() {
    this.isDragging = false;
    this.element.style.cursor = '';
  }

  zoomIn() {
    this.setZoom(this.zoom * 1.2);
  }

  zoomOut() {
    this.setZoom(this.zoom * 0.8);
  }

  setZoom(newZoom) {
    this.zoom = Math.max(0.1, Math.min(5, newZoom));
    this.updateTransform();
    this.updateZoomDisplay();
  }

  zoomAt(screenX, screenY, delta) {
    const rect = this.element.getBoundingClientRect();
    const x = screenX - rect.left;
    const y = screenY - rect.top;

    const oldZoom = this.zoom;
    const newZoom = Math.max(0.1, Math.min(5, oldZoom * delta));

    if (newZoom !== oldZoom) {
      // Adjust pan to zoom towards mouse position
      this.pan.x = x - (x - this.pan.x) * (newZoom / oldZoom);
      this.pan.y = y - (y - this.pan.y) * (newZoom / oldZoom);
      this.zoom = newZoom;

      this.updateTransform();
      this.updateZoomDisplay();
    }
  }

  resetZoom() {
    this.zoom = 1;
    this.pan = { x: 0, y: 0 };
    this.updateTransform();
    this.updateZoomDisplay();
  }

  zoomToFit() {
    // Get all nodes and calculate bounding box
    const nodes = this.nodesOverlay.querySelectorAll('.workflow-node');
    if (nodes.length === 0) {
      this.resetZoom();
      return;
    }

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    nodes.forEach(node => {
      const rect = node.getBoundingClientRect();
      const canvasRect = this.element.getBoundingClientRect();

      const x = rect.left - canvasRect.left;
      const y = rect.top - canvasRect.top;

      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x + rect.width);
      maxY = Math.max(maxY, y + rect.height);
    });

    const padding = 50;
    const contentWidth = maxX - minX + padding * 2;
    const contentHeight = maxY - minY + padding * 2;

    const canvasWidth = this.element.clientWidth;
    const canvasHeight = this.element.clientHeight;

    const scaleX = canvasWidth / contentWidth;
    const scaleY = canvasHeight / contentHeight;
    const scale = Math.min(scaleX, scaleY, 1); // Don't zoom in beyond 100%

    this.zoom = scale;
    this.pan.x = (canvasWidth - contentWidth * scale) / 2 - (minX - padding) * scale;
    this.pan.y = (canvasHeight - contentHeight * scale) / 2 - (minY - padding) * scale;

    this.updateTransform();
    this.updateZoomDisplay();
  }

  updateTransform() {
    // TEMPORARILY DISABLED to fix node dragging issues
    // const transform = `translate(${this.pan.x}px, ${this.pan.y}px) scale(${this.zoom})`;
    // this.nodesOverlay.style.transform = transform;

    // Only update SVG transform for connections
    const connectionsLayer = this.svg.querySelector('#connections-layer');
    if (connectionsLayer) {
      connectionsLayer.setAttribute('transform',
        `translate(${this.pan.x}, ${this.pan.y}) scale(${this.zoom})`);
    }

    console.log('Canvas transform update - pan:', this.pan, 'zoom:', this.zoom);
  }

  updateZoomDisplay() {
    const zoomLevel = document.querySelector('.zoom-level');
    if (zoomLevel) {
      zoomLevel.textContent = `${Math.round(this.zoom * 100)}%`;
    }
  }

  updateGrid() {
    const gridPattern = this.svg.querySelector('#grid');
    if (gridPattern) {
      gridPattern.setAttribute('width', this.gridSize);
      gridPattern.setAttribute('height', this.gridSize);
      gridPattern.style.display = this.showGridLines ? 'block' : 'none';
    }
  }

  showGrid() {
    this.showGridLines = true;
    this.updateGrid();
  }

  hideGrid() {
    this.showGridLines = false;
    this.updateGrid();
  }

  enableSnap() {
    this.snapToGrid = true;
  }

  disableSnap() {
    this.snapToGrid = false;
  }

  snapPosition(position) {
    return {
      x: Math.round(position.x / this.gridSize) * this.gridSize,
      y: Math.round(position.y / this.gridSize) * this.gridSize
    };
  }

  screenToCanvas(screenPos) {
    return {
      x: (screenPos.x - this.pan.x) / this.zoom,
      y: (screenPos.y - this.pan.y) / this.zoom
    };
  }

  canvasToScreen(canvasPos) {
    return {
      x: canvasPos.x * this.zoom + this.pan.x,
      y: canvasPos.y * this.zoom + this.pan.y
    };
  }

  clear() {
    // Clear all nodes from overlay
    this.nodesOverlay.innerHTML = '';

    // Clear all connections from SVG
    const connectionsLayer = this.svg.querySelector('#connections-layer');
    if (connectionsLayer) {
      connectionsLayer.innerHTML = '';
    }

    // Reset view
    this.resetZoom();
  }

  addNode(nodeElement) {
    this.nodesOverlay.appendChild(nodeElement);
  }

  removeNode(nodeElement) {
    if (nodeElement && nodeElement.parentNode === this.nodesOverlay) {
      this.nodesOverlay.removeChild(nodeElement);
    }
  }

  getCanvasRect() {
    return this.element.getBoundingClientRect();
  }

  getViewport() {
    return {
      x: -this.pan.x / this.zoom,
      y: -this.pan.y / this.zoom,
      width: this.element.clientWidth / this.zoom,
      height: this.element.clientHeight / this.zoom
    };
  }

  isInViewport(position, size = { width: 0, height: 0 }) {
    const viewport = this.getViewport();
    return !(
      position.x + size.width < viewport.x ||
      position.x > viewport.x + viewport.width ||
      position.y + size.height < viewport.y ||
      position.y > viewport.y + viewport.height
    );
  }

  centerOn(position) {
    const canvasCenter = {
      x: this.element.clientWidth / 2,
      y: this.element.clientHeight / 2
    };

    this.pan.x = canvasCenter.x - position.x * this.zoom;
    this.pan.y = canvasCenter.y - position.y * this.zoom;

    this.updateTransform();
  }

  // Connection drawing methods
  drawConnection(startPos, endPos, style = {}) {
    const connectionsLayer = this.svg.querySelector('#connections-layer');
    if (!connectionsLayer) return null;

    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    const pathData = this.createConnectionPath(startPos, endPos);

    path.setAttribute('d', pathData);
    path.setAttribute('stroke', style.color || '#666');
    path.setAttribute('stroke-width', style.width || '2');
    path.setAttribute('fill', 'none');
    path.setAttribute('stroke-linecap', 'round');

    if (style.dashed) {
      path.setAttribute('stroke-dasharray', '5,5');
    }

    connectionsLayer.appendChild(path);
    return path;
  }

  createConnectionPath(start, end) {
    const dx = end.x - start.x;
    const dy = end.y - start.y;

    // Create a smooth curve
    const cp1x = start.x + Math.abs(dx) * 0.5;
    const cp1y = start.y;
    const cp2x = end.x - Math.abs(dx) * 0.5;
    const cp2y = end.y;

    return `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`;
  }

  removeConnection(connectionElement) {
    if (connectionElement && connectionElement.parentNode) {
      connectionElement.parentNode.removeChild(connectionElement);
    }
  }

  // Selection rectangle
  startSelectionRectangle(startPos) {
    this.selectionStart = startPos;
    this.selectionRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    this.selectionRect.setAttribute('stroke', '#2196F3');
    this.selectionRect.setAttribute('stroke-width', '1');
    this.selectionRect.setAttribute('fill', 'rgba(33, 150, 243, 0.1)');
    this.selectionRect.setAttribute('stroke-dasharray', '3,3');

    const selectionLayer = this.svg.querySelector('#selection-layer');
    if (selectionLayer) {
      selectionLayer.appendChild(this.selectionRect);
    }
  }

  updateSelectionRectangle(currentPos) {
    if (!this.selectionRect || !this.selectionStart) return;

    const x = Math.min(this.selectionStart.x, currentPos.x);
    const y = Math.min(this.selectionStart.y, currentPos.y);
    const width = Math.abs(currentPos.x - this.selectionStart.x);
    const height = Math.abs(currentPos.y - this.selectionStart.y);

    this.selectionRect.setAttribute('x', x);
    this.selectionRect.setAttribute('y', y);
    this.selectionRect.setAttribute('width', width);
    this.selectionRect.setAttribute('height', height);
  }

  endSelectionRectangle() {
    if (this.selectionRect) {
      this.selectionRect.remove();
      this.selectionRect = null;
      this.selectionStart = null;
    }
  }

  // Utility methods
  getZoom() {
    return this.zoom;
  }

  getPan() {
    return { ...this.pan };
  }

  getGridSize() {
    return this.gridSize;
  }

  setGridSize(size) {
    this.gridSize = size;
    this.updateGrid();
  }
}


