# Dynamic Workflow System - 故障排除指南

## 🚨 主要问题：网页卡死，左侧图标栏无法加载

### 问题症状
- 网页加载后无响应
- 左侧节点调色板不显示
- F12开发者工具无法打开
- 浏览器标签页显示"无响应"

### 🔍 可能原因分析

#### 1. **JavaScript无限循环**
- 组件初始化过程中的递归调用
- 事件监听器的循环触发
- API请求的重试循环

#### 2. **API请求阻塞**
- `/api/nodes` 端点响应缓慢或失败
- 网络请求超时导致页面挂起
- CORS问题导致请求失败

#### 3. **内存泄漏**
- 大量DOM元素创建
- 事件监听器未正确清理
- 对象引用循环

#### 4. **组件初始化错误**
- 缺失的DOM元素引用
- 组件依赖关系错误
- 异步初始化竞态条件

### 🛠️ 解决方案

#### 方案1：使用安全模式页面
```
访问: http://localhost:3002/index-safe.html
```
- 简化的界面，基本功能正常
- 独立的JavaScript实现
- 无复杂组件依赖

#### 方案2：使用调试模式页面
```
访问: http://localhost:3002/index-debug.html
```
- 包含详细的调试信息
- 实时显示初始化进度
- 错误捕获和显示

#### 方案3：检查服务器状态
```bash
# 检查服务器是否运行
curl http://localhost:3002/api/health

# 检查节点API
curl http://localhost:3002/api/nodes
```

### 🔧 已实施的修复

#### 1. **添加超时保护**
```javascript
// API请求超时
const timeoutPromise = new Promise((_, reject) => {
  setTimeout(() => reject(new Error('Request timeout')), 10000);
});

// 组件初始化超时
await Promise.race([this.initializeComponents(), initTimeout]);
```

#### 2. **错误处理增强**
```javascript
try {
  this.components.nodePalette = new NodePalette(element, this.eventEmitter);
  console.log('✅ Node palette initialized');
} catch (error) {
  console.error('❌ Failed to initialize node palette:', error);
}
```

#### 3. **DOM元素检查**
```javascript
const nodeCategoriesElement = document.getElementById('node-categories');
if (nodeCategoriesElement) {
  // 安全初始化
} else {
  console.error('❌ node-categories element not found');
}
```

#### 4. **回退机制**
```javascript
// API失败时使用默认节点
const defaultNodes = this.getDefaultNodes();
this.components.nodePalette.loadNodes(defaultNodes);
```

### 📊 调试工具

#### 1. **浏览器控制台**
```javascript
// 检查应用状态
window.app
window.app.state
window.app.components

// 运行诊断
window.debugWorkflow.runDiagnostic()
```

#### 2. **网络面板**
- 检查API请求状态
- 查看响应时间
- 确认CORS设置

#### 3. **性能面板**
- 监控CPU使用率
- 检查内存使用
- 识别长时间运行的脚本

### 🎯 推荐解决步骤

#### 步骤1：确认服务器运行
```bash
node test-server.js
```

#### 步骤2：使用安全模式测试
```
http://localhost:3002/index-safe.html
```

#### 步骤3：使用调试模式诊断
```
http://localhost:3002/index-debug.html
```

#### 步骤4：检查浏览器控制台
- 查看错误信息
- 检查网络请求
- 监控性能指标

#### 步骤5：清除浏览器缓存
- 硬刷新页面 (Ctrl+F5)
- 清除浏览器缓存
- 禁用浏览器扩展

### 🔄 备用方案

#### 1. **简单测试页面**
```
http://localhost:3002/test-simple.html
```
- 最基础的拖拽功能
- 无复杂依赖
- 快速验证基本功能

#### 2. **静态文件服务**
```bash
# 使用Python简单服务器
python -m http.server 8000 --directory frontend

# 访问
http://localhost:8000/index-safe.html
```

#### 3. **本地文件访问**
- 直接打开 `frontend/test-simple.html`
- 无需服务器
- 基本功能测试

### 📝 常见错误及解决

#### 错误1：`Cannot read property of undefined`
**解决**: 添加空值检查
```javascript
if (element && element.querySelector) {
  // 安全操作
}
```

#### 错误2：`Network request failed`
**解决**: 检查服务器状态和CORS设置

#### 错误3：`Maximum call stack size exceeded`
**解决**: 检查递归调用和事件循环

#### 错误4：`Element not found`
**解决**: 确认DOM元素存在
```javascript
const element = document.getElementById('target');
if (!element) {
  console.error('Element not found');
  return;
}
```

### 🎉 成功指标

当问题解决后，你应该看到：
- ✅ 左侧节点调色板正常显示
- ✅ 可以拖拽节点到画布
- ✅ 缩放和平移功能正常
- ✅ F12开发者工具可以打开
- ✅ 浏览器响应正常

### 📞 进一步支持

如果问题仍然存在：
1. 检查浏览器版本兼容性
2. 尝试不同的浏览器
3. 检查系统资源使用情况
4. 查看详细的错误日志
