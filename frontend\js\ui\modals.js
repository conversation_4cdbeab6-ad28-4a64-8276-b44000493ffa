﻿/**
 * Modal Manager Component
 */

class ModalManager {
  showError(title, message) {
    alert(title + ': ' + message);
  }

  showWarning(title, message) {
    alert(title + ': ' + message);
  }

  showInfo(title, message) {
    alert(title + ': ' + message);
  }

  showConfirm(title, message, callback) {
    if (confirm(title + ': ' + message)) callback();
  }

  showWorkflowSelector(workflows, callback) {
    if (workflows.length > 0) callback(workflows[0]);
  }

  showExecutionResults(results) {
    console.log('Execution results:', results);
  }
}
