<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Workflow System - 安全完整版</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
            flex-direction: column;
        }
        
        /* Header */
        .app-header {
            height: 60px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .app-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .header-controls {
            margin-left: auto;
            display: flex;
            gap: 10px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: #d4edda;
            border-radius: 20px;
            font-size: 12px;
            color: #155724;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }
        
        /* Main Content */
        .app-main {
            flex: 1;
            display: flex;
        }
        
        /* Sidebar */
        .sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid #e1e5e9;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            background: #f8f9fa;
        }
        
        .sidebar-header h3 {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .node-category {
            margin-bottom: 20px;
        }
        
        .category-title {
            font-size: 14px;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .node-item {
            padding: 12px;
            margin: 6px 0;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            cursor: move;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
        }
        
        .node-item:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
        }
        
        .node-item.dragging {
            opacity: 0.6;
            transform: rotate(5deg);
        }
        
        .node-icon {
            font-size: 18px;
            width: 24px;
            text-align: center;
        }
        
        .node-info {
            flex: 1;
        }
        
        .node-name {
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .node-description {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }
        
        /* Canvas Area */
        .canvas-section {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .canvas-toolbar {
            height: 50px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            padding: 0 20px;
            gap: 10px;
        }
        
        .btn {
            padding: 6px 12px;
            border: 1px solid #e1e5e9;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }
        
        .btn:hover {
            background: #f8f9fa;
            border-color: #2196f3;
            color: #2196f3;
        }
        
        .btn.active {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }
        
        .zoom-info {
            margin-left: auto;
            font-size: 12px;
            color: #6c757d;
            padding: 6px 12px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .canvas-container {
            flex: 1;
            position: relative;
            background: #fafbfc;
            overflow: hidden;
        }
        
        .canvas {
            width: 100%;
            height: 100%;
            position: relative;
            cursor: grab;
            transform-origin: 0 0;
        }
        
        .canvas:active {
            cursor: grabbing;
        }
        
        .grid-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.4;
            background-image: 
                linear-gradient(to right, #e1e5e9 1px, transparent 1px),
                linear-gradient(to bottom, #e1e5e9 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
        }
        
        .workflow-node {
            position: absolute;
            min-width: 140px;
            min-height: 80px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            cursor: move;
            user-select: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 16px;
            text-align: center;
        }
        
        .workflow-node:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .workflow-node.dragging {
            opacity: 0.8;
            transform: rotate(3deg) scale(1.05);
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        }
        
        .workflow-node.selected {
            border-color: #ff9800;
            box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.2);
        }
        
        .node-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .node-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .node-subtitle {
            font-size: 12px;
            color: #6c757d;
        }
        
        /* Properties Panel */
        .properties-panel {
            width: 300px;
            background: white;
            border-left: 1px solid #e1e5e9;
            display: flex;
            flex-direction: column;
        }
        
        .properties-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            background: #f8f9fa;
        }
        
        .properties-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .no-selection {
            text-align: center;
            color: #6c757d;
            padding: 40px 20px;
        }
        
        .no-selection i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        /* Footer */
        .app-footer {
            height: 40px;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            padding: 0 20px;
            font-size: 12px;
            color: #6c757d;
        }
        
        .footer-info {
            display: flex;
            gap: 20px;
        }
        
        .footer-right {
            margin-left: auto;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <h1 class="app-title">
                <i class="fas fa-project-diagram"></i>
                Dynamic Workflow System
            </h1>
            
            <div class="header-controls">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>系统就绪</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Left Sidebar - Node Palette -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3>节点调色板</h3>
                    <input type="text" class="search-input" placeholder="搜索节点..." id="node-search">
                </div>

                <div class="sidebar-content" id="node-palette">
                    <!-- 节点将在这里动态加载 -->
                </div>
            </aside>

            <!-- Central Canvas -->
            <section class="canvas-section">
                <div class="canvas-toolbar">
                    <button class="btn" onclick="zoomIn()">
                        <i class="fas fa-search-plus"></i>
                        放大
                    </button>
                    <button class="btn" onclick="zoomOut()">
                        <i class="fas fa-search-minus"></i>
                        缩小
                    </button>
                    <button class="btn" onclick="resetView()">
                        <i class="fas fa-expand-arrows-alt"></i>
                        重置
                    </button>
                    <button class="btn" onclick="clearCanvas()">
                        <i class="fas fa-trash"></i>
                        清空
                    </button>
                    <button class="btn active" onclick="toggleGrid()" id="grid-btn">
                        <i class="fas fa-th"></i>
                        网格
                    </button>
                    
                    <div class="zoom-info">
                        缩放: <span id="zoom-display">100%</span>
                    </div>
                </div>

                <div class="canvas-container">
                    <div class="grid-background" id="grid"></div>
                    <div class="canvas" id="canvas">
                        <!-- 工作流节点将在这里创建 -->
                    </div>
                </div>
            </section>

            <!-- Right Sidebar - Properties Panel -->
            <aside class="properties-panel">
                <div class="properties-header">
                    <h3>属性面板</h3>
                </div>

                <div class="properties-content" id="properties">
                    <div class="no-selection">
                        <i class="fas fa-mouse-pointer"></i>
                        <p>选择一个节点来查看其属性</p>
                    </div>
                </div>
            </aside>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-info">
                <span>节点: <span id="node-count">0</span></span>
                <span>连接: <span id="connection-count">0</span></span>
            </div>
            
            <div class="footer-right">
                <span>Dynamic Workflow System v1.0.0 (安全版)</span>
            </div>
        </footer>
    </div>

    <script>
        // 全局状态
        let zoom = 1;
        let pan = { x: 0, y: 0 };
        let nodeCounter = 0;
        let selectedNode = null;
        let isDraggingCanvas = false;
        let isDraggingNode = false;
        let dragStart = { x: 0, y: 0 };
        let showGrid = true;
        let nodes = [];
        
        const canvas = document.getElementById('canvas');
        const grid = document.getElementById('grid');
        
        // 节点类型配置
        const nodeTypes = {
            'conditional-node': { 
                icon: '🔀', 
                name: '条件节点', 
                description: '基于条件进行分支',
                color: '#4caf50',
                category: '控制流'
            },
            'loop-node': { 
                icon: '🔄', 
                name: '循环节点', 
                description: '重复执行操作',
                color: '#ff9800',
                category: '控制流'
            },
            'custom-task-node': { 
                icon: '🔧', 
                name: '任务节点', 
                description: '执行自定义任务',
                color: '#2196f3',
                category: '任务'
            },
            'multi-branch-node': { 
                icon: '🌿', 
                name: '多分支节点', 
                description: '多路径分发',
                color: '#9c27b0',
                category: '控制流'
            },
            'input-node': { 
                icon: '📥', 
                name: '输入节点', 
                description: '数据输入',
                color: '#607d8b',
                category: '数据'
            },
            'output-node': { 
                icon: '📤', 
                name: '输出节点', 
                description: '数据输出',
                color: '#f44336',
                category: '数据'
            }
        };
        
        // 初始化应用
        async function init() {
            console.log('🚀 初始化安全完整版工作流系统');
            
            try {
                await loadNodes();
                setupEventListeners();
                renderNodePalette();
                updateDisplay();
                
                console.log('✅ 系统初始化完成');
            } catch (error) {
                console.error('❌ 初始化失败:', error);
            }
        }
        
        // 加载节点数据
        async function loadNodes() {
            try {
                const response = await fetch('/api/nodes');
                if (response.ok) {
                    const apiNodes = await response.json();
                    console.log(`📦 从API加载了 ${apiNodes.length} 个节点类型`);
                    
                    // 合并API节点和默认节点
                    apiNodes.forEach(node => {
                        if (!nodeTypes[node.id]) {
                            nodeTypes[node.id] = {
                                icon: node.visual?.icon?.value || '📦',
                                name: node.name,
                                description: node.description || '自定义节点',
                                color: node.visual?.icon?.color || '#666',
                                category: node.metadata?.category || '其他'
                            };
                        }
                    });
                } else {
                    console.warn('⚠️ API响应异常，使用默认节点');
                }
            } catch (error) {
                console.warn('⚠️ 无法连接API，使用默认节点:', error.message);
            }
        }
        
        // 渲染节点调色板
        function renderNodePalette() {
            const palette = document.getElementById('node-palette');
            palette.innerHTML = '';
            
            // 按类别分组
            const categories = {};
            Object.entries(nodeTypes).forEach(([id, config]) => {
                const category = config.category || '其他';
                if (!categories[category]) {
                    categories[category] = [];
                }
                categories[category].push({ id, ...config });
            });
            
            // 渲染每个类别
            Object.entries(categories).forEach(([categoryName, categoryNodes]) => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'node-category';
                
                const title = document.createElement('div');
                title.className = 'category-title';
                title.textContent = categoryName;
                categoryDiv.appendChild(title);
                
                categoryNodes.forEach(node => {
                    const item = document.createElement('div');
                    item.className = 'node-item';
                    item.draggable = true;
                    item.dataset.nodeType = node.id;
                    
                    item.innerHTML = `
                        <div class="node-icon">${node.icon}</div>
                        <div class="node-info">
                            <div class="node-name">${node.name}</div>
                            <div class="node-description">${node.description}</div>
                        </div>
                    `;
                    
                    // 拖拽事件
                    item.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/node-type', node.id);
                        item.classList.add('dragging');
                    });
                    
                    item.addEventListener('dragend', () => {
                        item.classList.remove('dragging');
                    });
                    
                    categoryDiv.appendChild(item);
                });
                
                palette.appendChild(categoryDiv);
            });
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 画布拖放
            canvas.addEventListener('dragover', (e) => {
                e.preventDefault();
            });
            
            canvas.addEventListener('drop', (e) => {
                e.preventDefault();
                const nodeType = e.dataTransfer.getData('text/node-type');
                if (nodeType) {
                    const rect = canvas.getBoundingClientRect();
                    const x = (e.clientX - rect.left - pan.x) / zoom;
                    const y = (e.clientY - rect.top - pan.y) / zoom;
                    createNode(nodeType, x, y);
                }
            });
            
            // 画布平移
            canvas.addEventListener('mousedown', (e) => {
                if (e.target === canvas || e.target === grid) {
                    isDraggingCanvas = true;
                    dragStart = { x: e.clientX - pan.x, y: e.clientY - pan.y };
                    canvas.style.cursor = 'grabbing';
                }
            });
            
            document.addEventListener('mousemove', (e) => {
                if (isDraggingCanvas) {
                    pan.x = e.clientX - dragStart.x;
                    pan.y = e.clientY - dragStart.y;
                    updateTransform();
                    updateDisplay();
                }
            });
            
            document.addEventListener('mouseup', () => {
                isDraggingCanvas = false;
                canvas.style.cursor = 'grab';
            });
            
            // 缩放
            canvas.addEventListener('wheel', (e) => {
                e.preventDefault();
                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                const rect = canvas.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                
                const oldZoom = zoom;
                zoom = Math.max(0.2, Math.min(3, zoom * delta));
                
                pan.x = mouseX - (mouseX - pan.x) * (zoom / oldZoom);
                pan.y = mouseY - (mouseY - pan.y) * (zoom / oldZoom);
                
                updateTransform();
                updateDisplay();
            });
            
            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Delete' && selectedNode) {
                    deleteNode(selectedNode);
                }
                if (e.key === 'Escape') {
                    clearSelection();
                }
            });
            
            // 搜索功能
            document.getElementById('node-search').addEventListener('input', (e) => {
                filterNodes(e.target.value);
            });
        }
        
        // 创建节点
        function createNode(type, x, y) {
            nodeCounter++;
            const config = nodeTypes[type];
            
            const node = document.createElement('div');
            node.className = 'workflow-node';
            node.dataset.nodeId = `${type}_${nodeCounter}`;
            node.dataset.nodeType = type;
            node.style.left = x + 'px';
            node.style.top = y + 'px';
            node.style.borderColor = config.color;
            
            node.innerHTML = `
                <div class="node-header">
                    <div class="node-icon" style="font-size: 20px;">${config.icon}</div>
                    <div>
                        <div class="node-title">${config.name}</div>
                        <div class="node-subtitle">#${nodeCounter}</div>
                    </div>
                </div>
            `;
            
            // 节点事件
            node.addEventListener('mousedown', (e) => {
                e.stopPropagation();
                selectNode(node);
                startNodeDrag(node, e);
            });
            
            canvas.appendChild(node);
            updateDisplay();
            
            console.log(`创建节点: ${config.name} #${nodeCounter}`);
        }
        
        // 节点拖拽
        function startNodeDrag(node, e) {
            isDraggingNode = true;
            const rect = node.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            
            const offsetX = e.clientX - rect.left;
            const offsetY = e.clientY - rect.top;
            
            node.classList.add('dragging');
            
            const mouseMoveHandler = (e) => {
                if (!isDraggingNode) return;
                
                const x = (e.clientX - canvasRect.left - offsetX - pan.x) / zoom;
                const y = (e.clientY - canvasRect.top - offsetY - pan.y) / zoom;
                
                node.style.left = Math.max(0, x) + 'px';
                node.style.top = Math.max(0, y) + 'px';
            };
            
            const mouseUpHandler = () => {
                isDraggingNode = false;
                node.classList.remove('dragging');
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            };
            
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);
        }
        
        // 节点选择
        function selectNode(node) {
            clearSelection();
            selectedNode = node;
            node.classList.add('selected');
            showNodeProperties(node);
        }
        
        function clearSelection() {
            if (selectedNode) {
                selectedNode.classList.remove('selected');
                selectedNode = null;
            }
            hideNodeProperties();
        }
        
        // 属性面板
        function showNodeProperties(node) {
            const properties = document.getElementById('properties');
            const config = nodeTypes[node.dataset.nodeType];
            
            properties.innerHTML = `
                <h4>节点属性</h4>
                <div style="margin: 20px 0;">
                    <label>名称:</label>
                    <input type="text" value="${config.name} #${node.dataset.nodeId.split('_')[1]}" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #e1e5e9; border-radius: 4px;">
                </div>
                <div style="margin: 20px 0;">
                    <label>类型:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">${config.name}</div>
                </div>
                <div style="margin: 20px 0;">
                    <label>描述:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">${config.description}</div>
                </div>
                <div style="margin: 20px 0;">
                    <button onclick="deleteNode(selectedNode)" style="width: 100%; padding: 10px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">删除节点</button>
                </div>
            `;
        }
        
        function hideNodeProperties() {
            const properties = document.getElementById('properties');
            properties.innerHTML = `
                <div class="no-selection">
                    <i class="fas fa-mouse-pointer"></i>
                    <p>选择一个节点来查看其属性</p>
                </div>
            `;
        }
        
        // 删除节点
        function deleteNode(node) {
            if (confirm('确定要删除这个节点吗？')) {
                node.remove();
                clearSelection();
                updateDisplay();
                console.log('删除节点: ' + node.dataset.nodeId);
            }
        }
        
        // 搜索过滤
        function filterNodes(query) {
            const items = document.querySelectorAll('.node-item');
            items.forEach(item => {
                const name = item.querySelector('.node-name').textContent.toLowerCase();
                const description = item.querySelector('.node-description').textContent.toLowerCase();
                const matches = name.includes(query.toLowerCase()) || description.includes(query.toLowerCase());
                item.style.display = matches ? 'flex' : 'none';
            });
        }
        
        // 变换更新
        function updateTransform() {
            canvas.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;
            grid.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;
        }
        
        // 显示更新
        function updateDisplay() {
            const nodeCount = canvas.querySelectorAll('.workflow-node').length;
            document.getElementById('node-count').textContent = nodeCount;
            document.getElementById('zoom-display').textContent = Math.round(zoom * 100) + '%';
        }
        
        // 工具栏功能
        function zoomIn() {
            zoom = Math.min(zoom * 1.2, 3);
            updateTransform();
            updateDisplay();
        }
        
        function zoomOut() {
            zoom = Math.max(zoom * 0.8, 0.2);
            updateTransform();
            updateDisplay();
        }
        
        function resetView() {
            zoom = 1;
            pan = { x: 0, y: 0 };
            updateTransform();
            updateDisplay();
        }
        
        function clearCanvas() {
            if (confirm('确定要清空所有节点吗？')) {
                canvas.querySelectorAll('.workflow-node').forEach(node => node.remove());
                clearSelection();
                nodeCounter = 0;
                updateDisplay();
                console.log('画布已清空');
            }
        }
        
        function toggleGrid() {
            showGrid = !showGrid;
            grid.style.display = showGrid ? 'block' : 'none';
            const btn = document.getElementById('grid-btn');
            btn.classList.toggle('active', showGrid);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
        
        console.log('安全完整版工作流系统脚本已加载');
    </script>
</body>
</html>
