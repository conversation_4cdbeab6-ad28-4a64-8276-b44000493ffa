﻿/**
 * Node Renderer for Dynamic Workflow System
 * Handles the visual rendering of workflow nodes with dynamic sizing and pin management
 */

class NodeRenderer {
  constructor(eventEmitter) {
    this.eventEmitter = eventEmitter;
    this.renderedNodes = new Map();
    this.nodeContainer = null;
    this.setupEventListeners();
  }

  setupEventListeners() {
    this.eventEmitter.on('node:create', (nodeData) => {
      this.renderNode(nodeData);
    });

    this.eventEmitter.on('node:update', (nodeData) => {
      this.updateNode(nodeData);
    });

    this.eventEmitter.on('node:delete', (nodeId) => {
      this.removeNode(nodeId);
    });

    this.eventEmitter.on('node:select', (nodeId) => {
      this.selectNode(nodeId);
    });

    this.eventEmitter.on('node:deselect', (nodeId) => {
      this.deselectNode(nodeId);
    });
  }

  setContainer(container) {
    this.nodeContainer = container;
  }

  setCanvas(canvas) {
    this.canvas = canvas;
  }

  renderNode(nodeData) {
    if (!this.nodeContainer) {
      console.error('Node container not set');
      return null;
    }

    // Create node element
    const nodeElement = this.createNodeElement(nodeData);

    // Add to container
    this.nodeContainer.appendChild(nodeElement);

    // Store reference
    this.renderedNodes.set(nodeData.id, {
      element: nodeElement,
      data: nodeData
    });

    // Setup node interactions
    this.setupNodeInteractions(nodeElement, nodeData);

    return nodeElement;
  }

  createNodeElement(nodeData) {
    const { visual, pins, name, type, id } = nodeData;

    // Calculate node dimensions based on pins
    const dimensions = this.calculateNodeDimensions(visual, pins);

    // Create main node container
    const nodeElement = document.createElement('div');
    nodeElement.className = `workflow-node ${type}`;
    nodeElement.id = `node-${id}`;
    nodeElement.style.width = `${dimensions.width}px`;
    nodeElement.style.height = `${dimensions.height}px`;
    nodeElement.style.left = `${nodeData.position?.x || 0}px`;
    nodeElement.style.top = `${nodeData.position?.y || 0}px`;

    // Apply visual styling
    this.applyVisualStyling(nodeElement, visual);

    // Create node header
    const header = this.createNodeHeader(nodeData);
    nodeElement.appendChild(header);

    // Create node body
    const body = this.createNodeBody(nodeData);
    nodeElement.appendChild(body);

    // Create pins
    const pinsContainer = this.createPins(nodeData, dimensions);
    nodeElement.appendChild(pinsContainer);

    return nodeElement;
  }

  createNodeHeader(nodeData) {
    const { visual, name, id } = nodeData;

    const header = document.createElement('div');
    header.className = 'node-header';

    // Icon
    const icon = document.createElement('span');
    icon.className = 'node-icon';

    if (visual.icon.type === 'unicode') {
      icon.textContent = visual.icon.value;
    } else if (visual.icon.type === 'fontawesome') {
      icon.className += ` ${visual.icon.value}`;
    }

    if (visual.icon.color) {
      icon.style.color = visual.icon.color;
    }

    // Title
    const title = document.createElement('span');
    title.className = 'node-title';
    title.textContent = name;
    title.title = name; // Tooltip for long names

    // Status indicator
    const status = document.createElement('span');
    status.className = 'node-status idle';
    status.id = `status-${id}`;

    header.appendChild(icon);
    header.appendChild(title);
    header.appendChild(status);

    return header;
  }

  createNodeBody(nodeData) {
    const body = document.createElement('div');
    body.className = 'node-body';

    // Node body is now empty - description will be shown in properties panel only
    // This keeps the node compact and clean
    console.log('NodeRenderer: createNodeBody - NOT showing description for node:', nodeData.id);

    return body;
  }

  createPins(nodeData, dimensions) {
    const { pins } = nodeData;
    const pinsContainer = document.createElement('div');
    pinsContainer.className = 'node-pins';

    // Create input pins
    if (pins.input && pins.input.definitions) {
      pins.input.definitions.forEach((pinDef, index) => {
        const pin = this.createPin('input', pinDef, index, pins.input.count, dimensions);
        pinsContainer.appendChild(pin);
      });
    }

    // Create output pins
    if (pins.output && pins.output.definitions) {
      pins.output.definitions.forEach((pinDef, index) => {
        const pin = this.createPin('output', pinDef, index, pins.output.count, dimensions);
        pinsContainer.appendChild(pin);
      });
    }

    return pinsContainer;
  }

  createPin(type, pinDef, index, totalPins, dimensions) {
    const pin = document.createElement('div');
    pin.className = `node-pin ${type}`;
    pin.id = `pin-${pinDef.name}`;
    pin.dataset.pinName = pinDef.name;
    pin.dataset.pinType = type;
    pin.dataset.dataType = pinDef.dataType;

    // Calculate pin position
    const position = this.calculatePinPosition(type, index, totalPins, dimensions);
    pin.style.top = `${position.y}px`;

    // Create pin label
    const label = document.createElement('div');
    label.className = `pin-label ${type}`;
    label.textContent = pinDef.label || pinDef.name;
    label.style.top = `${position.y}px`;

    // Add tooltip
    pin.title = `${pinDef.label || pinDef.name} (${pinDef.dataType})${pinDef.description ? '\n' + pinDef.description : ''}`;

    // Setup pin interactions
    this.setupPinInteractions(pin, pinDef);

    const container = document.createElement('div');
    container.appendChild(pin);
    container.appendChild(label);

    return container;
  }

  calculateNodeDimensions(visual, pins) {
    const baseWidth = visual.sizing.baseWidth || 120;
    const baseHeight = visual.sizing.baseHeight || 60;
    const pinSpacing = visual.sizing.pinSpacing || 20;

    let width = baseWidth;
    let height = baseHeight;

    if (visual.sizing.dynamicResize) {
      // Calculate height based on maximum pins on either side
      const maxPins = Math.max(
        pins.input?.count || 0,
        pins.output?.count || 0
      );

      if (maxPins > 0) {
        const requiredHeight = Math.max(
          baseHeight,
          (maxPins * pinSpacing) + 40 // 40px for header and padding
        );
        height = requiredHeight;
      }
    }

    return { width, height };
  }

  calculatePinPosition(type, index, totalPins, dimensions) {
    const headerHeight = 36; // Height of node header
    const availableHeight = dimensions.height - headerHeight - 20; // 20px padding

    let y;
    if (totalPins === 1) {
      y = headerHeight + (availableHeight / 2);
    } else {
      const spacing = availableHeight / (totalPins - 1);
      y = headerHeight + 10 + (index * spacing);
    }

    return { y };
  }

  applyVisualStyling(nodeElement, visual) {
    // Apply colors
    if (visual.colors) {
      if (visual.colors.background) {
        nodeElement.style.backgroundColor = visual.colors.background;
      }
      if (visual.colors.border) {
        nodeElement.style.borderColor = visual.colors.border;
      }
      if (visual.colors.text) {
        nodeElement.style.color = visual.colors.text;
      }
    }

    // Apply shape-specific styling
    if (visual.shape) {
      switch (visual.shape.type) {
        case 'circle':
          nodeElement.style.borderRadius = '50%';
          break;
        case 'diamond':
          nodeElement.classList.add('diamond-shape');
          break;
        case 'hexagon':
          nodeElement.classList.add('hexagon-shape');
          break;
        case 'custom':
          if (visual.shape.customPath) {
            // Apply custom SVG path styling
            nodeElement.style.clipPath = `path('${visual.shape.customPath}')`;
          }
          break;
      }
    }
  }

  setupNodeInteractions(nodeElement, nodeData) {
    // Node selection
    nodeElement.addEventListener('click', (e) => {
      e.stopPropagation();
      console.log('NodeRenderer: Node clicked:', nodeData.id, nodeData.name);
      this.eventEmitter.emit('node:clicked', nodeData.id);
    });

    // Node dragging - create unique handlers for each node
    this.setupNodeDragging(nodeElement, nodeData);

    // Context menu
    nodeElement.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      this.eventEmitter.emit('node:context-menu', {
        nodeId: nodeData.id,
        position: { x: e.clientX, y: e.clientY }
      });
    });
  }

  setupPinInteractions(pinElement, pinDef) {
    // Pin hover effects
    pinElement.addEventListener('mouseenter', () => {
      pinElement.classList.add('active');
      this.eventEmitter.emit('pin:hover', {
        pinName: pinDef.name,
        pinType: pinElement.dataset.pinType
      });
    });

    pinElement.addEventListener('mouseleave', () => {
      pinElement.classList.remove('active');
      this.eventEmitter.emit('pin:unhover', {
        pinName: pinDef.name,
        pinType: pinElement.dataset.pinType
      });
    });

    // Pin connection interactions
    pinElement.addEventListener('mousedown', (e) => {
      e.stopPropagation();
      this.eventEmitter.emit('pin:connection:start', {
        pinElement,
        pinName: pinDef.name,
        pinType: pinElement.dataset.pinType,
        dataType: pinDef.dataType,
        position: {
          x: e.clientX,
          y: e.clientY
        }
      });
    });
  }

  updateNode(nodeData) {
    const renderedNode = this.renderedNodes.get(nodeData.id);
    if (!renderedNode) return;

    // Update node data
    renderedNode.data = nodeData;

    // Update visual elements
    const nodeElement = renderedNode.element;

    // Update title
    const title = nodeElement.querySelector('.node-title');
    if (title) title.textContent = nodeData.name;

    // Update content
    const content = nodeElement.querySelector('.node-content');
    if (content) content.textContent = nodeData.description || '';

    // Update position if changed
    if (nodeData.position) {
      nodeElement.style.left = `${nodeData.position.x}px`;
      nodeElement.style.top = `${nodeData.position.y}px`;
    }

    this.eventEmitter.emit('node:updated', nodeData.id);
  }

  removeNode(nodeId) {
    const renderedNode = this.renderedNodes.get(nodeId);
    if (!renderedNode) return;

    // Remove from DOM
    renderedNode.element.remove();

    // Remove from tracking
    this.renderedNodes.delete(nodeId);

    this.eventEmitter.emit('node:removed', nodeId);
  }

  selectNode(nodeId) {
    const renderedNode = this.renderedNodes.get(nodeId);
    if (!renderedNode) return;

    renderedNode.element.classList.add('selected');
  }

  deselectNode(nodeId) {
    const renderedNode = this.renderedNodes.get(nodeId);
    if (!renderedNode) return;

    renderedNode.element.classList.remove('selected');
  }

  setNodeStatus(nodeId, status) {
    const renderedNode = this.renderedNodes.get(nodeId);
    if (!renderedNode) return;

    const statusElement = renderedNode.element.querySelector('.node-status');
    if (statusElement) {
      statusElement.className = `node-status ${status}`;
    }

    // Update node class for visual feedback
    const nodeElement = renderedNode.element;
    nodeElement.classList.remove('executing', 'error');

    if (status === 'running') {
      nodeElement.classList.add('executing');
    } else if (status === 'error') {
      nodeElement.classList.add('error');
    }
  }

  getNodeElement(nodeId) {
    const renderedNode = this.renderedNodes.get(nodeId);
    return renderedNode ? renderedNode.element : null;
  }

  getNodeData(nodeId) {
    const renderedNode = this.renderedNodes.get(nodeId);
    return renderedNode ? renderedNode.data : null;
  }

  getAllNodes() {
    return Array.from(this.renderedNodes.values());
  }

  clear() {
    this.renderedNodes.forEach((node) => {
      node.element.remove();
    });
    this.renderedNodes.clear();
  }

  // Utility method to get pin element
  getPinElement(nodeId, pinName) {
    const nodeElement = this.getNodeElement(nodeId);
    if (!nodeElement) return null;

    return nodeElement.querySelector(`[data-pin-name="${pinName}"]`);
  }

  // Method to highlight compatible pins during connection
  highlightCompatiblePins(sourcePin) {
    const sourceDataType = sourcePin.dataType;
    const sourcePinType = sourcePin.pinType;

    this.renderedNodes.forEach((node) => {
      const pins = node.element.querySelectorAll('.node-pin');
      pins.forEach((pin) => {
        const pinType = pin.dataset.pinType;
        const dataType = pin.dataset.dataType;

        // Highlight compatible pins (opposite type, compatible data)
        if (pinType !== sourcePinType &&
            (dataType === 'any' || sourceDataType === 'any' || dataType === sourceDataType)) {
          pin.classList.add('compatible');
        }
      });
    });
  }

  clearPinHighlights() {
    this.renderedNodes.forEach((node) => {
      const pins = node.element.querySelectorAll('.node-pin');
      pins.forEach((pin) => {
        pin.classList.remove('compatible', 'active');
      });
    });
  }

  setupNodeDragging(nodeElement, nodeData) {
    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };
    let initialPosition = { x: 0, y: 0 };
    let mouseMoveHandler = null;
    let mouseUpHandler = null;

    const startDrag = (e) => {
      if (e.target.classList.contains('node-pin')) return;

      isDragging = true;

      // Get current node position in canvas coordinates
      const currentLeft = parseFloat(nodeElement.style.left) || 0;
      const currentTop = parseFloat(nodeElement.style.top) || 0;

      // Store initial position
      initialPosition = { x: currentLeft, y: currentTop };

      // Calculate drag offset relative to mouse position
      const rect = nodeElement.getBoundingClientRect();
      const containerRect = this.nodeContainer.getBoundingClientRect();

      // Account for canvas transform when calculating offset
      const canvas = this.nodeContainer.closest('.workflow-canvas-container');
      const canvasRect = canvas.getBoundingClientRect();

      dragOffset.x = e.clientX - rect.left;
      dragOffset.y = e.clientY - rect.top;

      nodeElement.style.zIndex = '1000';
      nodeElement.classList.add('dragging');

      // Debug logging
      if (window.workflowDebugger) {
        window.workflowDebugger.log('info', `Starting drag for node: ${nodeData.id}`, {
          nodeId: nodeData.id,
          initialPosition: initialPosition,
          dragOffset: dragOffset,
          containerTransform: this.nodeContainer.style.transform
        });
      }

      // Create specific handlers for this drag operation
      mouseMoveHandler = (e) => {
        if (!isDragging) return;

        e.preventDefault();
        e.stopPropagation();

        // Get canvas and container info
        const canvas = this.nodeContainer.closest('.workflow-canvas-container');
        const canvasRect = canvas.getBoundingClientRect();

        // Calculate mouse position relative to canvas
        const mouseX = e.clientX - canvasRect.left;
        const mouseY = e.clientY - canvasRect.top;

        // Convert to node position (accounting for drag offset and canvas transform)
        const nodeX = mouseX - dragOffset.x;
        const nodeY = mouseY - dragOffset.y;

        // Apply position directly to the node element
        const clampedX = Math.max(0, nodeX);
        const clampedY = Math.max(0, nodeY);

        nodeElement.style.left = `${clampedX}px`;
        nodeElement.style.top = `${clampedY}px`;

        this.eventEmitter.emit('node:drag:move', {
          nodeId: nodeData.id,
          position: { x: clampedX, y: clampedY }
        });
      };

      mouseUpHandler = () => {
        if (!isDragging) return;

        isDragging = false;
        nodeElement.style.zIndex = '';
        nodeElement.classList.remove('dragging');

        // Get final position from element style (already in canvas coordinates)
        const finalPosition = {
          x: parseFloat(nodeElement.style.left) || 0,
          y: parseFloat(nodeElement.style.top) || 0
        };

        this.eventEmitter.emit('node:drag:end', {
          nodeId: nodeData.id,
          position: finalPosition
        });

        // Debug logging
        if (window.workflowDebugger) {
          window.workflowDebugger.log('info', `Drag ended for node: ${nodeData.id}`, {
            nodeId: nodeData.id,
            finalPosition: finalPosition
          });
        }

        // Clean up event listeners
        document.removeEventListener('mousemove', mouseMoveHandler);
        document.removeEventListener('mouseup', mouseUpHandler);
        mouseMoveHandler = null;
        mouseUpHandler = null;
      };

      // Add event listeners for this specific drag operation
      document.addEventListener('mousemove', mouseMoveHandler);
      document.addEventListener('mouseup', mouseUpHandler);

      this.eventEmitter.emit('node:drag:start', nodeData.id);
    };

    nodeElement.addEventListener('mousedown', startDrag);
  }
}
