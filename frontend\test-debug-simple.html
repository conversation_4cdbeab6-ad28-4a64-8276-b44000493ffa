<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Dynamic Workflow</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        
        .test-canvas {
            width: 800px;
            height: 600px;
            border: 2px solid #444;
            position: relative;
            background: #333;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .test-node {
            position: absolute;
            width: 120px;
            height: 80px;
            background: #4a90e2;
            border: 2px solid #5aa3f0;
            border-radius: 8px;
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            user-select: none;
        }
        
        .test-node:hover {
            background: #5aa3f0;
        }
        
        .test-node.dragging {
            opacity: 0.8;
            z-index: 1000;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #5aa3f0;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #333;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Debug Test - 拖动和缩放功能</h1>
        
        <div class="test-section">
            <h2>测试控制</h2>
            <div class="controls">
                <button class="btn" onclick="createNode()">创建节点</button>
                <button class="btn" onclick="zoomIn()">放大 (+)</button>
                <button class="btn" onclick="zoomOut()">缩小 (-)</button>
                <button class="btn" onclick="resetZoom()">重置缩放</button>
                <button class="btn" onclick="clearNodes()">清除节点</button>
            </div>
            <div class="status" id="status">准备就绪</div>
        </div>
        
        <div class="test-section">
            <h2>测试画布</h2>
            <div class="test-canvas" id="testCanvas">
                <div class="status" style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.7);">
                    缩放: <span id="zoomLevel">100%</span> | 节点: <span id="nodeCount">0</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let zoom = 1;
        let pan = { x: 0, y: 0 };
        let nodeCounter = 0;
        let isDragging = false;
        let dragTarget = null;
        let dragOffset = { x: 0, y: 0 };

        const canvas = document.getElementById('testCanvas');
        const status = document.getElementById('status');
        const zoomLevel = document.getElementById('zoomLevel');
        const nodeCount = document.getElementById('nodeCount');

        function log(message) {
            status.textContent = message;
            console.log('Debug:', message);
        }

        function updateDisplay() {
            zoomLevel.textContent = Math.round(zoom * 100) + '%';
            nodeCount.textContent = canvas.querySelectorAll('.test-node').length;
            
            // Apply transform to canvas
            canvas.style.transform = `scale(${zoom}) translate(${pan.x}px, ${pan.y}px)`;
        }

        function createNode() {
            nodeCounter++;
            const node = document.createElement('div');
            node.className = 'test-node';
            node.textContent = `节点 ${nodeCounter}`;
            node.style.left = (100 + nodeCounter * 30) + 'px';
            node.style.top = (100 + nodeCounter * 30) + 'px';
            
            // Add drag functionality
            node.addEventListener('mousedown', startDrag);
            
            canvas.appendChild(node);
            updateDisplay();
            log(`创建了节点 ${nodeCounter}`);
        }

        function startDrag(e) {
            if (e.target.classList.contains('test-node')) {
                isDragging = true;
                dragTarget = e.target;
                
                const rect = dragTarget.getBoundingClientRect();
                const canvasRect = canvas.getBoundingClientRect();
                
                dragOffset.x = e.clientX - rect.left;
                dragOffset.y = e.clientY - rect.top;
                
                dragTarget.classList.add('dragging');
                
                log(`开始拖动: ${dragTarget.textContent}`);
                e.preventDefault();
            }
        }

        function updateDrag(e) {
            if (isDragging && dragTarget) {
                const canvasRect = canvas.getBoundingClientRect();
                
                // Calculate position relative to canvas, accounting for zoom and pan
                const x = (e.clientX - canvasRect.left - dragOffset.x) / zoom - pan.x;
                const y = (e.clientY - canvasRect.top - dragOffset.y) / zoom - pan.y;
                
                dragTarget.style.left = Math.max(0, x) + 'px';
                dragTarget.style.top = Math.max(0, y) + 'px';
                
                log(`拖动到: (${Math.round(x)}, ${Math.round(y)})`);
            }
        }

        function endDrag() {
            if (isDragging && dragTarget) {
                dragTarget.classList.remove('dragging');
                log(`拖动结束: ${dragTarget.textContent}`);
                isDragging = false;
                dragTarget = null;
            }
        }

        function zoomIn() {
            zoom = Math.min(zoom * 1.2, 3);
            updateDisplay();
            log(`放大到: ${Math.round(zoom * 100)}%`);
        }

        function zoomOut() {
            zoom = Math.max(zoom * 0.8, 0.2);
            updateDisplay();
            log(`缩小到: ${Math.round(zoom * 100)}%`);
        }

        function resetZoom() {
            zoom = 1;
            pan = { x: 0, y: 0 };
            updateDisplay();
            log('重置缩放');
        }

        function clearNodes() {
            const nodes = canvas.querySelectorAll('.test-node');
            nodes.forEach(node => node.remove());
            nodeCounter = 0;
            updateDisplay();
            log('清除所有节点');
        }

        // Event listeners
        document.addEventListener('mousemove', updateDrag);
        document.addEventListener('mouseup', endDrag);

        // Mouse wheel zoom
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            zoom = Math.max(0.2, Math.min(3, zoom * delta));
            updateDisplay();
            log(`滚轮缩放到: ${Math.round(zoom * 100)}%`);
        });

        // Initialize
        updateDisplay();
        log('调试测试页面已加载');
    </script>
</body>
</html>
