const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const fs = require('fs-extra');

const DynamicNodeLoader = require('./utils/dynamicNodeLoader');
const { validateNodeConfig } = require('./utils/schemaValidator');

class DynamicWorkflowServer {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3000;
    this.nodeLoader = new DynamicNodeLoader();
    this.loadedNodes = new Map();

    this.setupMiddleware();
    this.setupStaticFiles();
    this.setupRoutes();
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
          fontSrc: ["'self'", "https://cdnjs.cloudflare.com"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.NODE_ENV === 'production' ? false : true,
      credentials: true
    }));

    // Logging
    this.app.use(morgan('combined'));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  }

  setupStaticFiles() {
    // Serve frontend files
    const frontendPath = path.join(__dirname, '../frontend');
    this.app.use(express.static(frontendPath));

    // Serve node configurations for frontend
    this.app.use('/nodes', express.static(path.join(__dirname, '../nodes')));
    this.app.use('/schema', express.static(path.join(__dirname, '../schema')));
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/api/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        loadedNodes: Array.from(this.loadedNodes.keys())
      });
    });

    // Get all available node types
    this.app.get('/api/nodes', (req, res) => {
      const nodeTypes = Array.from(this.loadedNodes.values()).map(node => ({
        id: node.id,
        name: node.name,
        type: node.type,
        description: node.description,
        metadata: node.metadata,
        pins: node.pins,
        visual: node.visual
      }));
      res.json(nodeTypes);
    });

    // Get specific node configuration
    this.app.get('/api/nodes/:nodeId', (req, res) => {
      const nodeId = req.params.nodeId;
      const node = this.loadedNodes.get(nodeId);

      if (!node) {
        return res.status(404).json({ error: 'Node not found' });
      }

      res.json(node);
    });

    // Workflow execution endpoint
    this.app.post('/api/workflow/execute', async (req, res) => {
      try {
        const { workflow, inputs } = req.body;

        if (!workflow || !workflow.nodes) {
          return res.status(400).json({ error: 'Invalid workflow structure' });
        }

        // Execute workflow (simplified for now)
        const results = await this.executeWorkflow(workflow, inputs);
        res.json({ success: true, results });
      } catch (error) {
        console.error('Workflow execution error:', error);
        res.status(500).json({ error: error.message });
      }
    });

    // Catch-all route for SPA
    this.app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../frontend/index.html'));
    });
  }

  async loadNodeConfigurations() {
    try {
      const nodesDir = path.join(__dirname, '../nodes');
      const nodeFiles = await fs.readdir(nodesDir);

      console.log('Loading node configurations...');

      for (const file of nodeFiles) {
        if (file.endsWith('.json')) {
          try {
            const filePath = path.join(nodesDir, file);
            const nodeConfig = await fs.readJson(filePath);

            // Validate node configuration
            const isValid = validateNodeConfig(nodeConfig);
            if (!isValid) {
              console.error(`Invalid node configuration in ${file}:`, validateNodeConfig.errors);
              continue;
            }

            // Register dynamic API endpoint
            await this.registerNodeEndpoint(nodeConfig);

            // Store node configuration
            this.loadedNodes.set(nodeConfig.id, nodeConfig);

            console.log(`✓ Loaded node: ${nodeConfig.name} (${nodeConfig.id})`);
          } catch (error) {
            console.error(`Error loading node configuration ${file}:`, error.message);
          }
        }
      }

      console.log(`Successfully loaded ${this.loadedNodes.size} node configurations`);
    } catch (error) {
      console.error('Error loading node configurations:', error);
    }
  }

  async registerNodeEndpoint(nodeConfig) {
    const { backend } = nodeConfig;
    const { endpoint, method, code } = backend;

    try {
      // Create the endpoint function from the code string
      const endpointFunction = new Function('req', 'res', 'next', code);

      // Register the route
      switch (method.toUpperCase()) {
        case 'GET':
          this.app.get(endpoint, endpointFunction);
          break;
        case 'POST':
          this.app.post(endpoint, endpointFunction);
          break;
        case 'PUT':
          this.app.put(endpoint, endpointFunction);
          break;
        case 'DELETE':
          this.app.delete(endpoint, endpointFunction);
          break;
        case 'PATCH':
          this.app.patch(endpoint, endpointFunction);
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      console.log(`✓ Registered ${method} ${endpoint}`);
    } catch (error) {
      console.error(`Error registering endpoint ${endpoint}:`, error.message);
      throw error;
    }
  }

  async executeWorkflow(workflow, inputs) {
    // Simplified workflow execution
    // In a full implementation, this would handle node dependencies and execution order
    const results = {};

    for (const nodeInstance of workflow.nodes) {
      try {
        const nodeConfig = this.loadedNodes.get(nodeInstance.type);
        if (!nodeConfig) {
          throw new Error(`Unknown node type: ${nodeInstance.type}`);
        }

        // Execute node with provided inputs
        const nodeResult = await this.executeNode(nodeConfig, nodeInstance.config, inputs);
        results[nodeInstance.id] = nodeResult;
      } catch (error) {
        console.error(`Error executing node ${nodeInstance.id}:`, error);
        results[nodeInstance.id] = { error: error.message };
      }
    }

    return results;
  }

  async executeNode(nodeConfig, instanceConfig, inputs) {
    // Create a safe execution context for the node
    const context = {
      inputs,
      config: instanceConfig,
      console: {
        log: (...args) => console.log(`[${nodeConfig.id}]`, ...args),
        error: (...args) => console.error(`[${nodeConfig.id}]`, ...args)
      }
    };

    // Execute the node's backend code
    const executeFunction = new Function(
      'inputs', 'config', 'console',
      `
      ${nodeConfig.backend.code}
      return typeof result !== 'undefined' ? result : null;
      `
    );

    return executeFunction(context.inputs, context.config, context.console);
  }

  async start() {
    try {
      // Load all node configurations first
      await this.loadNodeConfigurations();

      // Start the server
      this.app.listen(this.port, () => {
        console.log(`🚀 Dynamic Workflow Server running on port ${this.port}`);
        console.log(`📊 Frontend: http://localhost:${this.port}`);
        console.log(`🔌 API: http://localhost:${this.port}/api`);
        console.log(`📋 Health: http://localhost:${this.port}/api/health`);
        console.log(`📦 Loaded ${this.loadedNodes.size} node types`);
      });
    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  async stop() {
    console.log('Shutting down Dynamic Workflow Server...');
    process.exit(0);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Create and start the server
const server = new DynamicWorkflowServer();
server.start().catch(error => {
  console.error('Server startup failed:', error);
  process.exit(1);
});

module.exports = DynamicWorkflowServer;