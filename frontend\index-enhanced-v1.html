<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Workflow System - 增强版 v1.0 (连接系统)</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
            flex-direction: column;
        }
        
        /* Header */
        .app-header {
            height: 60px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .app-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .version-badge {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
        }
        
        .header-controls {
            margin-left: auto;
            display: flex;
            gap: 10px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: #d4edda;
            border-radius: 20px;
            font-size: 12px;
            color: #155724;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }
        
        /* Main Content */
        .app-main {
            flex: 1;
            display: flex;
        }
        
        /* Sidebar */
        .sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid #e1e5e9;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            background: #f8f9fa;
        }
        
        .sidebar-header h3 {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .node-category {
            margin-bottom: 20px;
        }
        
        .category-title {
            font-size: 14px;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .node-item {
            padding: 12px;
            margin: 6px 0;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            cursor: move;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
        }
        
        .node-item:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
        }
        
        .node-item.dragging {
            opacity: 0.6;
            transform: rotate(5deg);
        }
        
        .node-icon {
            font-size: 18px;
            width: 24px;
            text-align: center;
        }
        
        .node-info {
            flex: 1;
        }
        
        .node-name {
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .node-description {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }
        
        /* Canvas Area */
        .canvas-section {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .canvas-toolbar {
            height: 50px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            padding: 0 20px;
            gap: 10px;
        }
        
        .btn {
            padding: 6px 12px;
            border: 1px solid #e1e5e9;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }
        
        .btn:hover {
            background: #f8f9fa;
            border-color: #2196f3;
            color: #2196f3;
        }
        
        .btn.active {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }
        
        .zoom-info {
            margin-left: auto;
            font-size: 12px;
            color: #6c757d;
            padding: 6px 12px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .canvas-container {
            flex: 1;
            position: relative;
            background: #fafbfc;
            overflow: hidden;
            cursor: grab;
        }

        .canvas-container:active {
            cursor: grabbing;
        }
        
        .canvas {
            /* 无限画布：移除所有尺寸限制 */
            position: absolute;
            top: -50000px;
            left: -50000px;
            width: 100000px;
            height: 100000px;
            cursor: grab;
            transform-origin: 0 0;
            pointer-events: auto;
        }
        
        .canvas:active {
            cursor: grabbing;
        }
        
        .grid-background {
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            opacity: 0.4;
            background-image:
                linear-gradient(to right, #e1e5e9 1px, transparent 1px),
                linear-gradient(to bottom, #e1e5e9 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
        }
        
        /* SVG连接层 */
        .connections-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .connection-line {
            fill: none;
            stroke: #2196f3;
            stroke-width: 2;
            pointer-events: stroke;
            cursor: pointer;
        }
        
        .connection-line:hover {
            stroke: #ff9800;
            stroke-width: 3;
        }
        
        .connection-line.selected {
            stroke: #f44336;
            stroke-width: 3;
        }
        
        .temp-connection {
            stroke: #999;
            stroke-dasharray: 5,5;
            stroke-width: 2;
        }
        
        .workflow-node {
            position: absolute;
            min-width: 160px;
            min-height: 100px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            cursor: move;
            user-select: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;
            z-index: 2;
        }
        
        .workflow-node:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .workflow-node.dragging {
            opacity: 0.8;
            transform: rotate(3deg) scale(1.05);
            z-index: 1000;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        }
        
        .workflow-node.selected {
            border-color: #ff9800;
            box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.2);
        }
        
        .node-header {
            padding: 12px 16px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            gap: 10px;
            background: #f8f9fa;
            border-radius: 10px 10px 0 0;
        }
        
        .node-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            flex: 1;
        }
        
        .node-subtitle {
            font-size: 12px;
            color: #6c757d;
        }
        
        .node-body {
            padding: 16px;
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        /* Pin样式 */
        .node-pins {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .node-pins.input {
            align-items: flex-start;
        }
        
        .node-pins.output {
            align-items: flex-end;
        }
        
        .pin {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #6c757d;
            background: white;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }
        
        .pin:hover {
            transform: scale(1.3);
            border-color: #2196f3;
            background: #e3f2fd;
        }
        
        .pin.connected {
            background: #2196f3;
            border-color: #2196f3;
        }
        
        .pin.input {
            margin-left: -6px;
        }
        
        .pin.output {
            margin-right: -6px;
        }
        
        .pin-label {
            font-size: 10px;
            color: #6c757d;
            margin: 2px 8px;
            white-space: nowrap;
        }
        
        .pin-container {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .pin-container.input {
            flex-direction: row;
        }
        
        .pin-container.output {
            flex-direction: row-reverse;
        }
        
        /* Properties Panel */
        .properties-panel {
            width: 300px;
            background: white;
            border-left: 1px solid #e1e5e9;
            display: flex;
            flex-direction: column;
        }
        
        .properties-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            background: #f8f9fa;
        }
        
        .properties-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .no-selection {
            text-align: center;
            color: #6c757d;
            padding: 40px 20px;
        }
        
        .no-selection i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        /* Footer */
        .app-footer {
            height: 40px;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            align-items: center;
            padding: 0 20px;
            font-size: 12px;
            color: #6c757d;
        }
        
        .footer-info {
            display: flex;
            gap: 20px;
        }
        
        .footer-right {
            margin-left: auto;
        }
        
        /* 连接创建提示 */
        .connection-hint {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .connection-hint.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <h1 class="app-title">
                <i class="fas fa-project-diagram"></i>
                Dynamic Workflow System
                <span class="version-badge">v1.7 修复初始化错误</span>
            </h1>
            
            <div class="header-controls">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>无限画布 + AutoCAD式缩放</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Left Sidebar - Node Palette -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3>节点调色板</h3>
                    <input type="text" class="search-input" placeholder="搜索节点..." id="node-search">
                </div>

                <div class="sidebar-content" id="node-palette">
                    <!-- 节点将在这里动态加载 -->
                </div>
            </aside>

            <!-- Central Canvas -->
            <section class="canvas-section">
                <div class="canvas-toolbar">
                    <button class="btn" onclick="zoomIn()">
                        <i class="fas fa-search-plus"></i>
                        放大
                    </button>
                    <button class="btn" onclick="zoomOut()">
                        <i class="fas fa-search-minus"></i>
                        缩小
                    </button>
                    <button class="btn" onclick="resetView()">
                        <i class="fas fa-expand-arrows-alt"></i>
                        重置
                    </button>
                    <button class="btn" onclick="clearCanvas()">
                        <i class="fas fa-trash"></i>
                        清空
                    </button>
                    <button class="btn active" onclick="toggleGrid()" id="grid-btn">
                        <i class="fas fa-th"></i>
                        网格
                    </button>
                    <button class="btn" onclick="toggleConnectionMode()" id="connection-btn">
                        <i class="fas fa-link"></i>
                        连接模式
                    </button>
                    <button class="btn" onclick="testZoomFunction()" style="background: #ff9800; color: white;">
                        <i class="fas fa-vial"></i>
                        测试缩放
                    </button>
                    
                    <div class="zoom-info">
                        缩放: <span id="zoom-display">100%</span> |
                        节点: <span id="node-count">0</span> |
                        连接: <span id="connection-count">0</span> |
                        平移: <span id="pan-display">0, 0</span>
                    </div>
                </div>

                <div class="canvas-container">
                    <div class="grid-background" id="grid"></div>
                    
                    <!-- SVG连接层 -->
                    <svg class="connections-svg" id="connections-svg">
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                    refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2196f3" />
                            </marker>
                        </defs>
                        <g id="connections-group"></g>
                    </svg>
                    
                    <div class="canvas" id="canvas">
                        <!-- 工作流节点将在这里创建 -->
                    </div>
                    
                    <!-- 连接提示 -->
                    <div class="connection-hint" id="connection-hint">
                        点击输出Pin，然后点击目标输入Pin来创建连接
                    </div>
                </div>
            </section>

            <!-- Right Sidebar - Properties Panel -->
            <aside class="properties-panel">
                <div class="properties-header">
                    <h3>属性面板</h3>
                </div>

                <div class="properties-content" id="properties">
                    <div class="no-selection">
                        <i class="fas fa-mouse-pointer"></i>
                        <p>选择一个节点或连接来查看其属性</p>
                    </div>
                </div>
            </aside>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-info">
                <span>第一阶段: 连接系统</span>
                <span>新增: 无限画布 + AutoCAD式缩放 + 鼠标中心缩放</span>
            </div>
            
            <div class="footer-right">
                <span>Dynamic Workflow System v1.0 (增强版)</span>
            </div>
        </footer>
    </div>

    <script>
        console.log('🚀 Dynamic Workflow System v1.0 - 连接系统启动');
        
        // 全局状态
        let zoom = 1;
        let pan = { x: 0, y: 0 };
        let nodeCounter = 0;
        let connectionCounter = 0;
        let selectedNode = null;
        let selectedConnection = null;
        let isDraggingCanvas = false;
        let isDraggingNode = false;
        let isConnecting = false;
        let connectionMode = false;
        let dragStart = { x: 0, y: 0 };
        let showGrid = true;
        let nodes = [];
        let connections = [];
        let sourcePin = null;
        let tempConnection = null;
        
        const canvas = document.getElementById('canvas');
        const grid = document.getElementById('grid');
        const connectionsSvg = document.getElementById('connections-svg');
        const connectionsGroup = document.getElementById('connections-group');
        
        // 节点类型配置（增强版，包含Pin定义）
        const nodeTypes = {
            'conditional-node': { 
                icon: '🔀', 
                name: '条件节点', 
                description: '基于条件进行分支',
                color: '#4caf50',
                category: '控制流',
                pins: {
                    input: [
                        { name: 'condition', label: '条件', type: 'boolean' },
                        { name: 'input', label: '输入', type: 'any' }
                    ],
                    output: [
                        { name: 'true', label: '真', type: 'any' },
                        { name: 'false', label: '假', type: 'any' }
                    ]
                }
            },
            'loop-node': { 
                icon: '🔄', 
                name: '循环节点', 
                description: '重复执行操作',
                color: '#ff9800',
                category: '控制流',
                pins: {
                    input: [
                        { name: 'input', label: '输入', type: 'any' },
                        { name: 'count', label: '次数', type: 'number' }
                    ],
                    output: [
                        { name: 'output', label: '输出', type: 'any' },
                        { name: 'index', label: '索引', type: 'number' }
                    ]
                }
            },
            'custom-task-node': { 
                icon: '🔧', 
                name: '任务节点', 
                description: '执行自定义任务',
                color: '#2196f3',
                category: '任务',
                pins: {
                    input: [
                        { name: 'input', label: '输入', type: 'any' }
                    ],
                    output: [
                        { name: 'output', label: '输出', type: 'any' }
                    ]
                }
            },
            'multi-branch-node': { 
                icon: '🌿', 
                name: '多分支节点', 
                description: '多路径分发',
                color: '#9c27b0',
                category: '控制流',
                pins: {
                    input: [
                        { name: 'input', label: '输入', type: 'any' }
                    ],
                    output: [
                        { name: 'output1', label: '输出1', type: 'any' },
                        { name: 'output2', label: '输出2', type: 'any' },
                        { name: 'output3', label: '输出3', type: 'any' }
                    ]
                }
            },
            'input-node': { 
                icon: '📥', 
                name: '输入节点', 
                description: '数据输入',
                color: '#607d8b',
                category: '数据',
                pins: {
                    input: [],
                    output: [
                        { name: 'value', label: '值', type: 'any' }
                    ]
                }
            },
            'output-node': { 
                icon: '📤', 
                name: '输出节点', 
                description: '数据输出',
                color: '#f44336',
                category: '数据',
                pins: {
                    input: [
                        { name: 'input', label: '输入', type: 'any' }
                    ],
                    output: []
                }
            }
        };
        
        // 初始化应用
        async function init() {
            console.log('🔄 初始化连接系统...');
            
            try {
                await loadNodes();
                setupEventListeners();
                renderNodePalette();
                updateDisplay();
                
                console.log('✅ 连接系统初始化完成');
                showConnectionHint();
            } catch (error) {
                console.error('❌ 初始化失败:', error);
            }
        }
        
        // 显示连接提示
        function showConnectionHint() {
            const hint = document.getElementById('connection-hint');
            hint.classList.add('show');
            setTimeout(() => {
                hint.classList.remove('show');
            }, 3000);
        }

        // 测试缩放功能
        function testZoomFunction() {
            console.log('🧪 测试缩放功能...');

            // 清除之前的测试标记
            document.querySelectorAll('.zoom-test-marker').forEach(el => el.remove());

            // 创建一个测试节点在画布中心
            const rect = canvas.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            // 转换为画布坐标
            const canvasX = (centerX - pan.x) / zoom;
            const canvasY = (centerY - pan.y) / zoom;

            const testNode = createNode('custom-task-node', canvasX, canvasY);
            testNode.style.background = '#ff9800';
            testNode.style.border = '3px solid #f57c00';
            testNode.querySelector('.node-title').textContent = '缩放测试节点';
            testNode.classList.add('zoom-test-marker');

            // 创建十字标记在画布中心
            const crosshair = document.createElement('div');
            crosshair.className = 'zoom-test-marker';
            crosshair.style.cssText = `
                position: absolute;
                left: ${centerX - 10}px;
                top: ${centerY - 10}px;
                width: 20px;
                height: 20px;
                pointer-events: none;
                z-index: 1000;
                border: 2px solid red;
                background: rgba(255, 0, 0, 0.3);
            `;

            // 添加十字线
            const hLine = document.createElement('div');
            hLine.style.cssText = `
                position: absolute;
                left: -50px;
                top: 8px;
                width: 120px;
                height: 2px;
                background: red;
            `;

            const vLine = document.createElement('div');
            vLine.style.cssText = `
                position: absolute;
                left: 8px;
                top: -50px;
                width: 2px;
                height: 120px;
                background: red;
            `;

            crosshair.appendChild(hLine);
            crosshair.appendChild(vLine);
            document.querySelector('.canvas-container').appendChild(crosshair);

            console.log(`测试节点创建在画布坐标: (${canvasX.toFixed(1)}, ${canvasY.toFixed(1)})`);
            console.log('🎯 红色十字标记显示画布中心位置');
            console.log('📍 将鼠标放在红色十字上，然后滚动鼠标滚轮');
            console.log('✅ 如果缩放正确，十字应该始终保持在鼠标下方');
        }
        
        // 加载节点数据
        async function loadNodes() {
            try {
                const response = await fetch('/api/nodes');
                if (response.ok) {
                    const apiNodes = await response.json();
                    console.log(`📦 从API加载了 ${apiNodes.length} 个节点类型`);

                    // 合并API节点和默认节点
                    apiNodes.forEach(node => {
                        if (!nodeTypes[node.id] && node.pins) {
                            nodeTypes[node.id] = {
                                icon: node.visual?.icon?.value || '📦',
                                name: node.name,
                                description: node.description || '自定义节点',
                                color: node.visual?.icon?.color || '#666',
                                category: node.metadata?.category || '其他',
                                pins: node.pins
                            };
                        }
                    });
                } else {
                    console.warn('⚠️ API响应异常，使用默认节点');
                }
            } catch (error) {
                console.warn('⚠️ 无法连接API，使用默认节点:', error.message);
            }
        }

        // 渲染节点调色板
        function renderNodePalette() {
            const palette = document.getElementById('node-palette');
            palette.innerHTML = '';

            // 按类别分组
            const categories = {};
            Object.entries(nodeTypes).forEach(([id, config]) => {
                const category = config.category || '其他';
                if (!categories[category]) {
                    categories[category] = [];
                }
                categories[category].push({ id, ...config });
            });

            // 渲染每个类别
            Object.entries(categories).forEach(([categoryName, categoryNodes]) => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'node-category';

                const title = document.createElement('div');
                title.className = 'category-title';
                title.textContent = categoryName;
                categoryDiv.appendChild(title);

                categoryNodes.forEach(node => {
                    const item = document.createElement('div');
                    item.className = 'node-item';
                    item.draggable = true;
                    item.dataset.nodeType = node.id;

                    item.innerHTML = `
                        <div class="node-icon">${node.icon}</div>
                        <div class="node-info">
                            <div class="node-name">${node.name}</div>
                            <div class="node-description">${node.description}</div>
                        </div>
                    `;

                    // 拖拽事件
                    item.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/node-type', node.id);
                        item.classList.add('dragging');
                    });

                    item.addEventListener('dragend', () => {
                        item.classList.remove('dragging');
                    });

                    categoryDiv.appendChild(item);
                });

                palette.appendChild(categoryDiv);
            });
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 获取画布容器元素
            const canvasContainer = document.querySelector('.canvas-container');

            // 画布拖放
            canvas.addEventListener('dragover', (e) => {
                e.preventDefault();
            });

            canvas.addEventListener('drop', (e) => {
                e.preventDefault();
                const nodeType = e.dataTransfer.getData('text/node-type');
                if (nodeType) {
                    const rect = canvas.getBoundingClientRect();
                    const x = (e.clientX - rect.left - pan.x) / zoom;
                    const y = (e.clientY - rect.top - pan.y) / zoom;
                    createNode(nodeType, x, y);
                }
            });

            // 画布平移
            canvasContainer.addEventListener('mousedown', (e) => {
                // 只有在空白区域点击时才开始平移
                if (e.target === canvas || e.target === grid || e.target === canvasContainer) {
                    isDraggingCanvas = true;
                    dragStart = { x: e.clientX - pan.x, y: e.clientY - pan.y };
                    canvasContainer.style.cursor = 'grabbing';
                }
            });

            document.addEventListener('mousemove', (e) => {
                if (isDraggingCanvas) {
                    pan.x = e.clientX - dragStart.x;
                    pan.y = e.clientY - dragStart.y;
                    updateTransform();
                    updateDisplay();
                } else if (isConnecting && sourcePin) {
                    updateTempConnection(e);
                }
            });

            document.addEventListener('mouseup', () => {
                isDraggingCanvas = false;
                canvasContainer.style.cursor = 'grab';

                if (isConnecting && tempConnection) {
                    removeTempConnection();
                    isConnecting = false;
                    sourcePin = null;
                }
            });

            // 缩放（以鼠标为中心，类似AutoCAD）
            // 在整个画布容器上监听，确保任何位置都能缩放
            canvasContainer.addEventListener('wheel', (e) => {
                e.preventDefault();
                e.stopPropagation();

                try {
                    // 计算缩放因子
                    const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
                    const oldZoom = zoom;
                    const newZoom = Math.max(0.1, Math.min(5, zoom * scaleFactor));

                    // 如果缩放没有变化，直接返回
                    if (Math.abs(newZoom - oldZoom) < 0.001) {
                        console.log('缩放已达到极限');
                        return;
                    }

                    // 获取画布容器的边界矩形
                    const canvasContainer = document.querySelector('.canvas-container');
                    const rect = canvasContainer.getBoundingClientRect();

                    // 计算鼠标相对于画布容器的位置
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;

                    // 验证鼠标位置是否有效
                    if (isNaN(mouseX) || isNaN(mouseY)) {
                        console.error('无效的鼠标位置');
                        return;
                    }

                    // 正确的鼠标中心缩放算法
                    // 1. 计算鼠标在当前变换下的世界坐标
                    const worldX = (mouseX - pan.x) / oldZoom;
                    const worldY = (mouseY - pan.y) / oldZoom;

                    // 2. 更新缩放级别
                    zoom = newZoom;

                    // 3. 重新计算平移，使世界坐标点在屏幕上的位置保持不变
                    pan.x = mouseX - worldX * newZoom;
                    pan.y = mouseY - worldY * newZoom;

                    // 验证计算结果
                    if (isNaN(pan.x) || isNaN(pan.y)) {
                        console.error('平移计算错误，恢复原值');
                        zoom = oldZoom;
                        return;
                    }

                    // 应用变换并更新显示
                    updateTransform();
                    updateDisplay();
                    updateAllConnections();

                    // 验证缩放是否正确：重新计算鼠标的世界坐标，应该与之前相同
                    const verifyWorldX = (mouseX - pan.x) / newZoom;
                    const verifyWorldY = (mouseY - pan.y) / newZoom;
                    const errorX = Math.abs(worldX - verifyWorldX);
                    const errorY = Math.abs(worldY - verifyWorldY);

                    // 调试信息
                    console.log(`✅ 缩放: ${oldZoom.toFixed(2)} → ${newZoom.toFixed(2)}, 鼠标: (${mouseX.toFixed(1)}, ${mouseY.toFixed(1)}), 平移: (${pan.x.toFixed(1)}, ${pan.y.toFixed(1)}), 误差: (${errorX.toFixed(3)}, ${errorY.toFixed(3)})`);

                } catch (error) {
                    console.error('缩放过程中发生错误:', error);
                }
            });

            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Delete') {
                    if (selectedNode) {
                        deleteNode(selectedNode);
                    } else if (selectedConnection) {
                        deleteConnection(selectedConnection);
                    }
                }
                if (e.key === 'Escape') {
                    clearSelection();
                    if (isConnecting) {
                        cancelConnection();
                    }
                }
                if (e.key === 'c' && e.ctrlKey) {
                    toggleConnectionMode();
                }
            });

            // 搜索功能
            document.getElementById('node-search').addEventListener('input', (e) => {
                filterNodes(e.target.value);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);

        // 创建节点
        function createNode(type, x, y) {
            nodeCounter++;
            const config = nodeTypes[type];

            const node = document.createElement('div');
            node.className = 'workflow-node';
            node.dataset.nodeId = `${type}_${nodeCounter}`;
            node.dataset.nodeType = type;
            node.style.left = x + 'px';
            node.style.top = y + 'px';
            node.style.borderColor = config.color;

            // 创建节点结构
            const header = document.createElement('div');
            header.className = 'node-header';
            header.innerHTML = `
                <div class="node-icon" style="font-size: 16px;">${config.icon}</div>
                <div class="node-title">${config.name}</div>
                <div class="node-subtitle">#${nodeCounter}</div>
            `;

            const body = document.createElement('div');
            body.className = 'node-body';

            // 创建输入Pin
            const inputPins = document.createElement('div');
            inputPins.className = 'node-pins input';
            config.pins.input.forEach((pinConfig, index) => {
                const pinContainer = createPin(pinConfig, 'input', node.dataset.nodeId, index);
                inputPins.appendChild(pinContainer);
            });

            // 创建输出Pin
            const outputPins = document.createElement('div');
            outputPins.className = 'node-pins output';
            config.pins.output.forEach((pinConfig, index) => {
                const pinContainer = createPin(pinConfig, 'output', node.dataset.nodeId, index);
                outputPins.appendChild(pinContainer);
            });

            body.appendChild(inputPins);
            body.appendChild(outputPins);

            node.appendChild(header);
            node.appendChild(body);

            // 节点事件
            node.addEventListener('mousedown', (e) => {
                if (e.target.classList.contains('pin')) return;
                e.stopPropagation();
                selectNode(node);
                startNodeDrag(node, e);
            });

            canvas.appendChild(node);
            updateDisplay();

            console.log(`创建节点: ${config.name} #${nodeCounter}`);
            return node;
        }

        // 创建Pin
        function createPin(pinConfig, direction, nodeId, index) {
            const container = document.createElement('div');
            container.className = `pin-container ${direction}`;

            const pin = document.createElement('div');
            pin.className = `pin ${direction}`;
            pin.dataset.pinId = `${nodeId}_${direction}_${index}`;
            pin.dataset.pinName = pinConfig.name;
            pin.dataset.pinType = pinConfig.type;
            pin.dataset.nodeId = nodeId;
            pin.dataset.direction = direction;

            const label = document.createElement('div');
            label.className = 'pin-label';
            label.textContent = pinConfig.label;

            // Pin点击事件
            pin.addEventListener('click', (e) => {
                e.stopPropagation();
                handlePinClick(pin);
            });

            // Pin悬停事件
            pin.addEventListener('mouseenter', () => {
                if (isConnecting && sourcePin && canConnect(sourcePin, pin)) {
                    pin.style.background = '#4caf50';
                    pin.style.borderColor = '#4caf50';
                }
            });

            pin.addEventListener('mouseleave', () => {
                if (!pin.classList.contains('connected')) {
                    pin.style.background = 'white';
                    pin.style.borderColor = '#6c757d';
                }
            });

            if (direction === 'input') {
                container.appendChild(pin);
                container.appendChild(label);
            } else {
                container.appendChild(label);
                container.appendChild(pin);
            }

            return container;
        }

        // 处理Pin点击
        function handlePinClick(pin) {
            if (!connectionMode && !isConnecting) {
                toggleConnectionMode();
            }

            if (!isConnecting) {
                // 开始连接
                if (pin.dataset.direction === 'output') {
                    startConnection(pin);
                }
            } else {
                // 完成连接
                if (pin.dataset.direction === 'input' && sourcePin && canConnect(sourcePin, pin)) {
                    createConnection(sourcePin, pin);
                }
                cancelConnection();
            }
        }

        // 开始连接
        function startConnection(outputPin) {
            sourcePin = outputPin;
            isConnecting = true;
            outputPin.style.background = '#ff9800';
            outputPin.style.borderColor = '#ff9800';

            // 创建临时连接线
            createTempConnection(outputPin);

            console.log('开始连接:', outputPin.dataset.pinId);
        }

        // 取消连接
        function cancelConnection() {
            if (sourcePin) {
                if (!sourcePin.classList.contains('connected')) {
                    sourcePin.style.background = 'white';
                    sourcePin.style.borderColor = '#6c757d';
                }
                sourcePin = null;
            }

            if (tempConnection) {
                removeTempConnection();
            }

            isConnecting = false;
            console.log('取消连接');
        }

        // 检查是否可以连接
        function canConnect(outputPin, inputPin) {
            // 不能连接到同一个节点
            if (outputPin.dataset.nodeId === inputPin.dataset.nodeId) {
                return false;
            }

            // 输出Pin只能连接到输入Pin
            if (outputPin.dataset.direction !== 'output' || inputPin.dataset.direction !== 'input') {
                return false;
            }

            // 检查是否已经存在连接
            const existingConnection = connections.find(conn =>
                conn.input.pinId === inputPin.dataset.pinId
            );

            if (existingConnection) {
                return false;
            }

            // 类型兼容性检查（简化版）
            const outputType = outputPin.dataset.pinType;
            const inputType = inputPin.dataset.pinType;

            if (outputType === 'any' || inputType === 'any' || outputType === inputType) {
                return true;
            }

            return false;
        }

        // 创建连接
        function createConnection(outputPin, inputPin) {
            connectionCounter++;

            const connection = {
                id: `connection_${connectionCounter}`,
                output: {
                    nodeId: outputPin.dataset.nodeId,
                    pinId: outputPin.dataset.pinId,
                    pinName: outputPin.dataset.pinName,
                    element: outputPin
                },
                input: {
                    nodeId: inputPin.dataset.nodeId,
                    pinId: inputPin.dataset.pinId,
                    pinName: inputPin.dataset.pinName,
                    element: inputPin
                },
                element: null
            };

            // 创建SVG连接线
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('class', 'connection-line');
            path.setAttribute('marker-end', 'url(#arrowhead)');
            path.dataset.connectionId = connection.id;

            // 连接线点击事件
            path.addEventListener('click', (e) => {
                e.stopPropagation();
                selectConnection(connection);
            });

            connection.element = path;
            connectionsGroup.appendChild(path);
            connections.push(connection);

            // 更新Pin状态
            outputPin.classList.add('connected');
            inputPin.classList.add('connected');
            outputPin.style.background = '#2196f3';
            outputPin.style.borderColor = '#2196f3';
            inputPin.style.background = '#2196f3';
            inputPin.style.borderColor = '#2196f3';

            // 更新连接线路径
            updateConnectionPath(connection);
            updateDisplay();

            console.log(`创建连接: ${outputPin.dataset.pinId} -> ${inputPin.dataset.pinId}`);
            return connection;
        }

        // 更新连接线路径
        function updateConnectionPath(connection) {
            const outputPin = connection.output.element;
            const inputPin = connection.input.element;

            if (!outputPin || !inputPin) return;

            const outputRect = outputPin.getBoundingClientRect();
            const inputRect = inputPin.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();

            // 计算Pin在SVG坐标系中的位置（不需要除以zoom，因为SVG已经应用了transform）
            const startX = outputRect.left + outputRect.width / 2 - canvasRect.left;
            const startY = outputRect.top + outputRect.height / 2 - canvasRect.top;
            const endX = inputRect.left + inputRect.width / 2 - canvasRect.left;
            const endY = inputRect.top + inputRect.height / 2 - canvasRect.top;

            // 创建贝塞尔曲线路径
            const dx = endX - startX;
            const controlOffset = Math.max(50, Math.abs(dx) * 0.5);

            const path = `M ${startX} ${startY} C ${startX + controlOffset} ${startY}, ${endX - controlOffset} ${endY}, ${endX} ${endY}`;

            connection.element.setAttribute('d', path);
        }

        // 更新所有连接线
        function updateAllConnections() {
            connections.forEach(connection => {
                updateConnectionPath(connection);
            });
        }

        // 创建临时连接线
        function createTempConnection(outputPin) {
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('class', 'temp-connection');
            tempConnection = path;
            connectionsGroup.appendChild(path);
        }

        // 更新临时连接线
        function updateTempConnection(mouseEvent) {
            if (!tempConnection || !sourcePin) return;

            const outputRect = sourcePin.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();

            // 计算起点和终点在SVG坐标系中的位置
            const startX = outputRect.left + outputRect.width / 2 - canvasRect.left;
            const startY = outputRect.top + outputRect.height / 2 - canvasRect.top;
            const endX = mouseEvent.clientX - canvasRect.left;
            const endY = mouseEvent.clientY - canvasRect.top;

            const dx = endX - startX;
            const controlOffset = Math.max(50, Math.abs(dx) * 0.5);

            const path = `M ${startX} ${startY} C ${startX + controlOffset} ${startY}, ${endX - controlOffset} ${endY}, ${endX} ${endY}`;

            tempConnection.setAttribute('d', path);
        }

        // 移除临时连接线
        function removeTempConnection() {
            if (tempConnection) {
                connectionsGroup.removeChild(tempConnection);
                tempConnection = null;
            }
        }

        // 删除连接
        function deleteConnection(connection) {
            if (confirm('确定要删除这个连接吗？')) {
                // 移除SVG元素
                if (connection.element && connection.element.parentNode) {
                    connection.element.parentNode.removeChild(connection.element);
                }

                // 更新Pin状态
                const outputPin = connection.output.element;
                const inputPin = connection.input.element;

                if (outputPin) {
                    outputPin.classList.remove('connected');
                    outputPin.style.background = 'white';
                    outputPin.style.borderColor = '#6c757d';
                }

                if (inputPin) {
                    inputPin.classList.remove('connected');
                    inputPin.style.background = 'white';
                    inputPin.style.borderColor = '#6c757d';
                }

                // 从数组中移除
                const index = connections.indexOf(connection);
                if (index > -1) {
                    connections.splice(index, 1);
                }

                clearSelection();
                updateDisplay();

                console.log('删除连接:', connection.id);
            }
        }

        // 节点拖拽
        function startNodeDrag(node, e) {
            isDraggingNode = true;

            // 获取节点当前在画布坐标系中的位置
            const currentLeft = parseFloat(node.style.left) || 0;
            const currentTop = parseFloat(node.style.top) || 0;

            // 计算鼠标相对于画布的位置（考虑缩放和平移）
            const canvasRect = canvas.getBoundingClientRect();
            const mouseCanvasX = (e.clientX - canvasRect.left - pan.x) / zoom;
            const mouseCanvasY = (e.clientY - canvasRect.top - pan.y) / zoom;

            // 计算鼠标相对于节点的偏移（在画布坐标系中）
            const offsetX = mouseCanvasX - currentLeft;
            const offsetY = mouseCanvasY - currentTop;

            node.classList.add('dragging');

            const mouseMoveHandler = (e) => {
                if (!isDraggingNode) return;

                // 计算鼠标在画布坐标系中的新位置
                const newMouseCanvasX = (e.clientX - canvasRect.left - pan.x) / zoom;
                const newMouseCanvasY = (e.clientY - canvasRect.top - pan.y) / zoom;

                // 计算节点的新位置（减去偏移量）
                const newX = newMouseCanvasX - offsetX;
                const newY = newMouseCanvasY - offsetY;

                // 无限画布：移除位置限制，允许节点移动到任何位置
                node.style.left = newX + 'px';
                node.style.top = newY + 'px';

                // 更新相关连接线
                updateNodeConnections(node.dataset.nodeId);

                // 更新显示信息（包括坐标）
                updateDisplay();
            };

            const mouseUpHandler = () => {
                isDraggingNode = false;
                node.classList.remove('dragging');
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            };

            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);
        }

        // 更新节点相关的连接线
        function updateNodeConnections(nodeId) {
            connections.forEach(connection => {
                if (connection.output.nodeId === nodeId || connection.input.nodeId === nodeId) {
                    updateConnectionPath(connection);
                }
            });
        }

        // 节点选择
        function selectNode(node) {
            clearSelection();
            selectedNode = node;
            node.classList.add('selected');
            showNodeProperties(node);
        }

        // 连接选择
        function selectConnection(connection) {
            clearSelection();
            selectedConnection = connection;
            connection.element.classList.add('selected');
            showConnectionProperties(connection);
        }

        function clearSelection() {
            if (selectedNode) {
                selectedNode.classList.remove('selected');
                selectedNode = null;
            }

            if (selectedConnection) {
                selectedConnection.element.classList.remove('selected');
                selectedConnection = null;
            }

            hideProperties();
        }

        // 属性面板
        function showNodeProperties(node) {
            const properties = document.getElementById('properties');
            const config = nodeTypes[node.dataset.nodeType];

            properties.innerHTML = `
                <h4>节点属性</h4>
                <div style="margin: 20px 0;">
                    <label>名称:</label>
                    <input type="text" value="${config.name} #${node.dataset.nodeId.split('_')[1]}"
                           style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #e1e5e9; border-radius: 4px;">
                </div>
                <div style="margin: 20px 0;">
                    <label>类型:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">${config.name}</div>
                </div>
                <div style="margin: 20px 0;">
                    <label>描述:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">${config.description}</div>
                </div>
                <div style="margin: 20px 0;">
                    <label>输入Pin (${config.pins.input.length}):</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">
                        ${config.pins.input.map(pin => `${pin.label} (${pin.type})`).join('<br>') || '无'}
                    </div>
                </div>
                <div style="margin: 20px 0;">
                    <label>输出Pin (${config.pins.output.length}):</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">
                        ${config.pins.output.map(pin => `${pin.label} (${pin.type})`).join('<br>') || '无'}
                    </div>
                </div>
                <div style="margin: 20px 0;">
                    <button onclick="deleteNode(selectedNode)"
                            style="width: 100%; padding: 10px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        删除节点
                    </button>
                </div>
            `;
        }

        function showConnectionProperties(connection) {
            const properties = document.getElementById('properties');

            properties.innerHTML = `
                <h4>连接属性</h4>
                <div style="margin: 20px 0;">
                    <label>连接ID:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">${connection.id}</div>
                </div>
                <div style="margin: 20px 0;">
                    <label>输出:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">
                        ${connection.output.nodeId} → ${connection.output.pinName}
                    </div>
                </div>
                <div style="margin: 20px 0;">
                    <label>输入:</label>
                    <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">
                        ${connection.input.nodeId} → ${connection.input.pinName}
                    </div>
                </div>
                <div style="margin: 20px 0;">
                    <button onclick="deleteConnection(selectedConnection)"
                            style="width: 100%; padding: 10px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        删除连接
                    </button>
                </div>
            `;
        }

        function hideProperties() {
            const properties = document.getElementById('properties');
            properties.innerHTML = `
                <div class="no-selection">
                    <i class="fas fa-mouse-pointer"></i>
                    <p>选择一个节点或连接来查看其属性</p>
                </div>
            `;
        }

        // 删除节点
        function deleteNode(node) {
            if (confirm('确定要删除这个节点吗？这将同时删除所有相关连接。')) {
                // 删除相关连接
                const nodeConnections = connections.filter(conn =>
                    conn.output.nodeId === node.dataset.nodeId ||
                    conn.input.nodeId === node.dataset.nodeId
                );

                nodeConnections.forEach(connection => {
                    deleteConnection(connection);
                });

                // 删除节点
                node.remove();
                clearSelection();
                updateDisplay();
                console.log('删除节点: ' + node.dataset.nodeId);
            }
        }

        // 搜索过滤
        function filterNodes(query) {
            const items = document.querySelectorAll('.node-item');
            items.forEach(item => {
                const name = item.querySelector('.node-name').textContent.toLowerCase();
                const description = item.querySelector('.node-description').textContent.toLowerCase();
                const matches = name.includes(query.toLowerCase()) || description.includes(query.toLowerCase());
                item.style.display = matches ? 'flex' : 'none';
            });
        }

        // 变换更新
        function updateTransform() {
            canvas.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;
            grid.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;

            // SVG连接层不需要transform，因为连接线的坐标是基于屏幕坐标计算的
            // 这样可以避免双重变换的问题
        }

        // 显示更新
        function updateDisplay() {
            const nodeCount = canvas.querySelectorAll('.workflow-node').length;
            const connectionCount = connections.length;

            document.getElementById('node-count').textContent = nodeCount;
            document.getElementById('connection-count').textContent = connectionCount;
            document.getElementById('zoom-display').textContent = Math.round(zoom * 100) + '%';
            document.getElementById('pan-display').textContent = `${Math.round(pan.x)}, ${Math.round(pan.y)}`;
        }

        // 工具栏功能
        function zoomIn() {
            zoomToPoint(1.2, null); // 以画布中心缩放
        }

        function zoomOut() {
            zoomToPoint(0.8, null); // 以画布中心缩放
        }

        // 通用缩放函数，支持指定缩放中心点
        function zoomToPoint(factor, centerPoint) {
            const oldZoom = zoom;
            const newZoom = Math.max(0.1, Math.min(5, zoom * factor));

            if (Math.abs(newZoom - oldZoom) < 0.001) return;

            // 如果没有指定中心点，使用画布中心
            if (!centerPoint) {
                const rect = canvas.getBoundingClientRect();
                centerPoint = {
                    x: rect.width / 2,
                    y: rect.height / 2
                };
            }

            // 使用相同的简化缩放算法
            const zoomRatio = newZoom / oldZoom;

            // 调整平移量，使中心点位置保持不变
            const newPanX = centerPoint.x - (centerPoint.x - pan.x) * zoomRatio;
            const newPanY = centerPoint.y - (centerPoint.y - pan.y) * zoomRatio;

            // 应用新的缩放和平移
            zoom = newZoom;
            pan.x = newPanX;
            pan.y = newPanY;

            updateTransform();
            updateDisplay();
            updateAllConnections();
        }

        function resetView() {
            zoom = 1;
            pan = { x: 0, y: 0 };
            updateTransform();
            updateDisplay();
            updateAllConnections();
        }

        function clearCanvas() {
            if (confirm('确定要清空所有节点和连接吗？')) {
                // 清除所有连接
                connections.forEach(connection => {
                    if (connection.element && connection.element.parentNode) {
                        connection.element.parentNode.removeChild(connection.element);
                    }
                });
                connections = [];

                // 清除所有节点
                canvas.querySelectorAll('.workflow-node').forEach(node => node.remove());

                clearSelection();
                nodeCounter = 0;
                connectionCounter = 0;
                updateDisplay();
                console.log('画布已清空');
            }
        }

        function toggleGrid() {
            showGrid = !showGrid;
            grid.style.display = showGrid ? 'block' : 'none';
            const btn = document.getElementById('grid-btn');
            btn.classList.toggle('active', showGrid);
        }

        function toggleConnectionMode() {
            connectionMode = !connectionMode;
            const btn = document.getElementById('connection-btn');
            btn.classList.toggle('active', connectionMode);

            if (connectionMode) {
                console.log('连接模式已启用');
                showConnectionHint();
            } else {
                console.log('连接模式已禁用');
                if (isConnecting) {
                    cancelConnection();
                }
            }
        }

        // 键盘快捷键提示
        function showKeyboardShortcuts() {
            alert(`键盘快捷键和操作指南:

🎮 基本操作:
Delete - 删除选中的节点或连接
Escape - 取消当前操作/清除选择
Ctrl+C - 切换连接模式

🔍 缩放和导航:
鼠标滚轮 - 以鼠标为中心缩放 (AutoCAD式)
工具栏 +/- - 以画布中心缩放
拖拽空白区域 - 平移画布

🔗 连接操作:
点击输出Pin → 点击输入Pin - 创建连接
点击连接线 - 选中连接

🎨 画布特性:
• 无限大画布 - 节点可移动到任何位置
• 缩放范围: 10% - 500%
• 支持负坐标位置`);
        }

        // 导出工作流数据
        function exportWorkflow() {
            const workflow = {
                nodes: Array.from(canvas.querySelectorAll('.workflow-node')).map(node => ({
                    id: node.dataset.nodeId,
                    type: node.dataset.nodeType,
                    position: {
                        x: parseFloat(node.style.left),
                        y: parseFloat(node.style.top)
                    }
                })),
                connections: connections.map(conn => ({
                    id: conn.id,
                    output: {
                        nodeId: conn.output.nodeId,
                        pinName: conn.output.pinName
                    },
                    input: {
                        nodeId: conn.input.nodeId,
                        pinName: conn.input.pinName
                    }
                }))
            };

            const blob = new Blob([JSON.stringify(workflow, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `workflow-${new Date().toISOString().slice(0, 19)}.json`;
            a.click();
            URL.revokeObjectURL(url);

            console.log('工作流已导出');
        }

        // 添加工具栏按钮事件
        document.addEventListener('DOMContentLoaded', () => {
            // 添加导出按钮
            const toolbar = document.querySelector('.canvas-toolbar');
            const exportBtn = document.createElement('button');
            exportBtn.className = 'btn';
            exportBtn.innerHTML = '<i class="fas fa-download"></i> 导出';
            exportBtn.onclick = exportWorkflow;

            const helpBtn = document.createElement('button');
            helpBtn.className = 'btn';
            helpBtn.innerHTML = '<i class="fas fa-question"></i> 帮助';
            helpBtn.onclick = showKeyboardShortcuts;

            toolbar.insertBefore(exportBtn, toolbar.querySelector('.zoom-info'));
            toolbar.insertBefore(helpBtn, toolbar.querySelector('.zoom-info'));
        });

        console.log('Dynamic Workflow System v1.0 - 连接系统脚本已加载');
    </script>
</body>
</html>
