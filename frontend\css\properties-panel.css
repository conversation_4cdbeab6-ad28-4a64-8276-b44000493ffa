﻿/* Properties Panel Styles for Dynamic Workflow System */

.properties-panel {
    height: 100%;
    overflow-y: auto;
}

.no-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-secondary);
    text-align: center;
}

.no-selection i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.no-selection p {
    font-size: 14px;
    margin: 0;
}

/* Property Groups */
.property-group {
    margin-bottom: 24px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    overflow: hidden;
}

.property-group-header {
    background: var(--bg-primary);
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    font-size: 13px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.property-group-header:hover {
    background: var(--bg-secondary);
}

.property-group-header .toggle-icon {
    transition: transform 0.2s ease;
}

.property-group.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.property-group-content {
    padding: 16px;
    background: var(--bg-secondary);
}

.property-group.collapsed .property-group-content {
    display: none;
}

/* Individual Properties */
.property-item {
    margin-bottom: 16px;
}

.property-item:last-child {
    margin-bottom: 0;
}

.property-label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.property-label.required::after {
    content: " *";
    color: var(--error-color);
}

.property-description {
    font-size: 11px;
    color: var(--text-secondary);
    margin-top: 4px;
    line-height: 1.4;
}

/* Form Controls */
.property-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: border-color 0.2s ease;
}

.property-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.property-input:invalid {
    border-color: var(--error-color);
}

.property-input[type="number"] {
    text-align: right;
}

.property-textarea {
    min-height: 80px;
    resize: vertical;
    font-family: 'Courier New', monospace;
}

.property-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 32px;
}

.property-checkbox {
    width: auto;
    margin-right: 8px;
}

.property-checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.property-checkbox-wrapper:hover {
    color: var(--primary-color);
}

.property-color {
    width: 60px;
    height: 32px;
    padding: 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.property-file {
    padding: 6px;
}

/* Range Slider */
.property-range {
    width: 100%;
    margin: 8px 0;
}

.range-value {
    display: inline-block;
    min-width: 40px;
    text-align: right;
    font-size: 11px;
    color: var(--text-secondary);
    margin-left: 8px;
}

/* Button Groups */
.property-button-group {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.property-button {
    flex: 1;
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.property-button:hover {
    background: var(--bg-primary);
    border-color: var(--primary-color);
}

.property-button.active {
    background: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

/* Validation States */
.property-item.error .property-input {
    border-color: var(--error-color);
    background: rgba(244, 67, 54, 0.05);
}

.property-item.warning .property-input {
    border-color: var(--warning-color);
    background: rgba(255, 152, 0, 0.05);
}

.property-error {
    font-size: 11px;
    color: var(--error-color);
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.property-warning {
    font-size: 11px;
    color: var(--warning-color);
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Multi-Selection Panel */
.multi-selection-panel {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
}

.multi-selection-count {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.multi-selection-actions {
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Node Info Section */
.node-info {
    background: var(--bg-primary);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
}

.node-info-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.node-info-icon {
    font-size: 16px;
    margin-right: 8px;
}

.node-info-title {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
}

.node-info-type {
    font-size: 11px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.node-info-description {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
    margin-top: 8px;
}

/* Pin Configuration */
.pin-config {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 8px;
}

.pin-config-header {
    background: var(--bg-primary);
    padding: 8px 12px;
    border-bottom: 1px solid var(--border-color);
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.pin-config-content {
    padding: 12px;
}

.pin-type-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    background: var(--text-secondary);
    color: var(--text-light);
}

.pin-type-badge.input {
    background: var(--info-color);
}

.pin-type-badge.output {
    background: var(--success-color);
}

/* Advanced Section */
.advanced-section {
    border-top: 1px solid var(--border-color);
    margin-top: 24px;
    padding-top: 16px;
}

.advanced-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 16px;
}

.advanced-toggle:hover {
    color: var(--primary-color);
}

.advanced-content {
    display: none;
}

.advanced-content.visible {
    display: block;
}

/* Responsive */
@media (max-width: 768px) {
    .property-group-content {
        padding: 12px;
    }

    .property-input {
        padding: 6px 10px;
    }

    .property-button-group {
        flex-direction: column;
    }
}
