/**
 * Debug Tool for Dynamic Workflow System
 * Comprehensive debugging and diagnostics
 */

class WorkflowDebugger {
  constructor() {
    this.logs = [];
    this.enabled = true;
    this.startTime = Date.now();
    
    this.init();
  }

  init() {
    // Add debug panel to page
    this.createDebugPanel();
    
    // Override console methods to capture logs
    this.interceptConsole();
    
    // Monitor DOM changes
    this.monitorDOM();
    
    // Add global debug functions
    window.debugWorkflow = this;
  }

  log(level, message, data = null) {
    if (!this.enabled) return;
    
    const timestamp = Date.now() - this.startTime;
    const logEntry = {
      timestamp,
      level,
      message,
      data: data ? JSON.parse(JSON.stringify(data)) : null
    };
    
    this.logs.push(logEntry);
    this.updateDebugPanel();
    
    // Also log to console with styling
    const style = this.getLogStyle(level);
    console.log(`%c[${timestamp}ms] ${level.toUpperCase()}: ${message}`, style, data || '');
  }

  getLogStyle(level) {
    const styles = {
      error: 'color: #F44336; font-weight: bold;',
      warn: 'color: #FF9800; font-weight: bold;',
      info: 'color: #2196F3;',
      debug: 'color: #9E9E9E;',
      success: 'color: #4CAF50; font-weight: bold;'
    };
    return styles[level] || styles.debug;
  }

  createDebugPanel() {
    const panel = document.createElement('div');
    panel.id = 'debug-panel';
    panel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 400px;
      height: 300px;
      background: rgba(0,0,0,0.9);
      color: white;
      font-family: monospace;
      font-size: 12px;
      border-radius: 5px;
      z-index: 10000;
      overflow: hidden;
      display: none;
    `;
    
    const header = document.createElement('div');
    header.style.cssText = `
      background: #333;
      padding: 5px 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `;
    header.innerHTML = `
      <span>Workflow Debugger</span>
      <div>
        <button onclick="debugWorkflow.clear()" style="margin-right: 5px;">Clear</button>
        <button onclick="debugWorkflow.toggle()">Hide</button>
      </div>
    `;
    
    const content = document.createElement('div');
    content.id = 'debug-content';
    content.style.cssText = `
      padding: 10px;
      height: calc(100% - 40px);
      overflow-y: auto;
    `;
    
    panel.appendChild(header);
    panel.appendChild(content);
    document.body.appendChild(panel);
    
    // Add toggle button
    const toggleBtn = document.createElement('button');
    toggleBtn.innerHTML = '🐛';
    toggleBtn.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 10001;
      background: #2196F3;
      color: white;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      cursor: pointer;
    `;
    toggleBtn.onclick = () => this.toggle();
    document.body.appendChild(toggleBtn);
  }

  toggle() {
    const panel = document.getElementById('debug-panel');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
  }

  clear() {
    this.logs = [];
    this.updateDebugPanel();
  }

  updateDebugPanel() {
    const content = document.getElementById('debug-content');
    if (!content) return;
    
    const recent = this.logs.slice(-50); // Show last 50 logs
    content.innerHTML = recent.map(log => 
      `<div style="margin-bottom: 2px; ${this.getLogStyle(log.level)}">
        [${log.timestamp}ms] ${log.level.toUpperCase()}: ${log.message}
        ${log.data ? '<br><pre>' + JSON.stringify(log.data, null, 2) + '</pre>' : ''}
      </div>`
    ).join('');
    
    content.scrollTop = content.scrollHeight;
  }

  interceptConsole() {
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    
    console.log = (...args) => {
      this.log('debug', args.join(' '));
      originalLog.apply(console, args);
    };
    
    console.error = (...args) => {
      this.log('error', args.join(' '));
      originalError.apply(console, args);
    };
    
    console.warn = (...args) => {
      this.log('warn', args.join(' '));
      originalWarn.apply(console, args);
    };
  }

  monitorDOM() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1 && node.classList && node.classList.contains('workflow-node')) {
              this.log('info', `Node added to DOM: ${node.id}`);
            }
          });
        }
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // Diagnostic methods
  checkNodeStructure() {
    const nodes = document.querySelectorAll('.workflow-node');
    this.log('info', `Found ${nodes.length} nodes in DOM`);
    
    nodes.forEach((node, index) => {
      const rect = node.getBoundingClientRect();
      const style = window.getComputedStyle(node);
      
      this.log('debug', `Node ${index + 1}: ${node.id}`, {
        position: style.position,
        left: style.left,
        top: style.top,
        transform: style.transform,
        rect: {
          x: rect.x,
          y: rect.y,
          width: rect.width,
          height: rect.height
        }
      });
    });
  }

  checkEventListeners() {
    const canvas = document.getElementById('workflow-canvas');
    const nodesOverlay = document.getElementById('nodes-overlay');
    const propertiesPanel = document.getElementById('properties-panel');
    
    this.log('info', 'Checking key elements', {
      canvas: !!canvas,
      nodesOverlay: !!nodesOverlay,
      propertiesPanel: !!propertiesPanel,
      canvasTransform: canvas ? window.getComputedStyle(nodesOverlay).transform : 'N/A'
    });
  }

  checkAppState() {
    if (window.app) {
      this.log('info', 'App state', {
        selectedNodes: Array.from(window.app.state.selectedNodes),
        components: Object.keys(window.app.components)
      });
    } else {
      this.log('error', 'App instance not found on window');
    }
  }

  runFullDiagnostic() {
    this.log('success', '=== STARTING FULL DIAGNOSTIC ===');
    this.checkNodeStructure();
    this.checkEventListeners();
    this.checkAppState();
    this.log('success', '=== DIAGNOSTIC COMPLETE ===');
  }
}

// Initialize debugger when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.workflowDebugger = new WorkflowDebugger();
  });
} else {
  window.workflowDebugger = new WorkflowDebugger();
}
