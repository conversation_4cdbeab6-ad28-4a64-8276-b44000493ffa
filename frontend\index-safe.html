<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Workflow System - Safe Mode</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #fafafa;
        }
        
        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .main-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .toolbar {
            height: 50px;
            background: white;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            padding: 0 20px;
            gap: 10px;
        }
        
        .canvas-area {
            flex: 1;
            position: relative;
            background: #fafafa;
            overflow: hidden;
        }
        
        .node-item {
            padding: 12px;
            margin: 8px 0;
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            cursor: move;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.2s ease;
        }
        
        .node-item:hover {
            background: #bbdefb;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .node-icon {
            font-size: 18px;
            width: 24px;
            text-align: center;
        }
        
        .workflow-node {
            position: absolute;
            min-width: 120px;
            min-height: 60px;
            background: white;
            border: 2px solid #2196f3;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: move;
            user-select: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
            font-weight: 500;
        }
        
        .workflow-node:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }
        
        .workflow-node.dragging {
            opacity: 0.8;
            transform: rotate(2deg) scale(1.02);
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .canvas {
            width: 100%;
            height: 100%;
            position: relative;
            transform-origin: 0 0;
            cursor: grab;
        }
        
        .canvas:active {
            cursor: grabbing;
        }
        
        button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        button:hover {
            background: #f0f0f0;
            border-color: #2196f3;
        }
        
        .status {
            margin-left: auto;
            padding: 4px 8px;
            background: #4caf50;
            color: white;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            color: #f44336;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>Node Palette</h3>
            </div>
            <div class="sidebar-content">
                <div id="node-list">
                    <div class="loading">Loading nodes...</div>
                </div>
            </div>
        </div>
        
        <div class="main-area">
            <div class="toolbar">
                <button onclick="zoomIn()">🔍+ Zoom In</button>
                <button onclick="zoomOut()">🔍- Zoom Out</button>
                <button onclick="resetView()">🎯 Reset</button>
                <button onclick="clearCanvas()">🗑️ Clear</button>
                <span id="zoom-display">100%</span>
                <div class="status" id="status">Ready</div>
            </div>
            
            <div class="canvas-area">
                <div class="canvas" id="canvas">
                    <!-- Nodes will be added here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 Starting Dynamic Workflow System - Safe Mode');
        
        // Global state
        let zoom = 1;
        let pan = { x: 0, y: 0 };
        let nodeCounter = 0;
        let isDraggingCanvas = false;
        let dragStart = { x: 0, y: 0 };
        let nodes = [];
        
        const canvas = document.getElementById('canvas');
        const nodeList = document.getElementById('node-list');
        const status = document.getElementById('status');
        
        // Initialize the application
        async function init() {
            try {
                status.textContent = 'Loading...';
                await loadNodes();
                setupEventListeners();
                status.textContent = 'Ready';
                console.log('✅ Application initialized successfully');
            } catch (error) {
                console.error('❌ Failed to initialize:', error);
                status.textContent = 'Error';
                showError('Failed to load application: ' + error.message);
            }
        }
        
        // Load nodes from API
        async function loadNodes() {
            try {
                const response = await fetch('/api/nodes');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                nodes = await response.json();
                console.log(`✅ Loaded ${nodes.length} node types`);
                renderNodePalette();
            } catch (error) {
                console.error('Failed to load nodes:', error);
                // Fallback to default nodes
                nodes = getDefaultNodes();
                renderNodePalette();
                showError('Using default nodes - server connection failed');
            }
        }
        
        // Default nodes if API fails
        function getDefaultNodes() {
            return [
                {
                    id: 'test-node',
                    name: 'Test Node',
                    visual: { icon: { type: 'unicode', value: '🔧' } }
                },
                {
                    id: 'conditional-node',
                    name: 'Conditional',
                    visual: { icon: { type: 'unicode', value: '🔀' } }
                },
                {
                    id: 'loop-node',
                    name: 'Loop',
                    visual: { icon: { type: 'unicode', value: '🔄' } }
                }
            ];
        }
        
        // Render node palette
        function renderNodePalette() {
            nodeList.innerHTML = '';
            
            nodes.forEach(node => {
                const item = document.createElement('div');
                item.className = 'node-item';
                item.draggable = true;
                item.dataset.nodeType = node.id;
                
                const icon = document.createElement('span');
                icon.className = 'node-icon';
                icon.textContent = node.visual?.icon?.value || '📦';
                
                const name = document.createElement('span');
                name.textContent = node.name;
                
                item.appendChild(icon);
                item.appendChild(name);
                nodeList.appendChild(item);
                
                // Setup drag
                item.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/node-type', node.id);
                    e.dataTransfer.setData('text/node-name', node.name);
                });
            });
        }
        
        // Setup event listeners
        function setupEventListeners() {
            // Canvas drop handling
            canvas.addEventListener('dragover', (e) => {
                e.preventDefault();
            });
            
            canvas.addEventListener('drop', (e) => {
                e.preventDefault();
                const nodeType = e.dataTransfer.getData('text/node-type');
                const nodeName = e.dataTransfer.getData('text/node-name');
                if (nodeType) {
                    const rect = canvas.getBoundingClientRect();
                    const x = (e.clientX - rect.left - pan.x) / zoom;
                    const y = (e.clientY - rect.top - pan.y) / zoom;
                    createNode(nodeType, nodeName, x, y);
                }
            });
            
            // Canvas panning
            canvas.addEventListener('mousedown', (e) => {
                if (e.target === canvas) {
                    isDraggingCanvas = true;
                    dragStart = { x: e.clientX - pan.x, y: e.clientY - pan.y };
                    canvas.style.cursor = 'grabbing';
                }
            });
            
            document.addEventListener('mousemove', (e) => {
                if (isDraggingCanvas) {
                    pan.x = e.clientX - dragStart.x;
                    pan.y = e.clientY - dragStart.y;
                    updateTransform();
                }
            });
            
            document.addEventListener('mouseup', () => {
                isDraggingCanvas = false;
                canvas.style.cursor = 'grab';
            });
            
            // Zoom with mouse wheel
            canvas.addEventListener('wheel', (e) => {
                e.preventDefault();
                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                const rect = canvas.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                
                const oldZoom = zoom;
                zoom = Math.max(0.2, Math.min(3, zoom * delta));
                
                // Zoom towards mouse position
                pan.x = mouseX - (mouseX - pan.x) * (zoom / oldZoom);
                pan.y = mouseY - (mouseY - pan.y) * (zoom / oldZoom);
                
                updateTransform();
                updateZoomDisplay();
            });
        }
        
        // Create a new node
        function createNode(type, name, x, y) {
            nodeCounter++;
            const node = document.createElement('div');
            node.className = 'workflow-node';
            node.textContent = `${name} ${nodeCounter}`;
            node.style.left = x + 'px';
            node.style.top = y + 'px';
            node.dataset.nodeId = `${type}_${nodeCounter}`;
            
            setupNodeDragging(node);
            canvas.appendChild(node);
            
            console.log(`Created node: ${name} at (${x}, ${y})`);
        }
        
        // Setup node dragging
        function setupNodeDragging(node) {
            let isDragging = false;
            let dragOffset = { x: 0, y: 0 };
            
            node.addEventListener('mousedown', (e) => {
                isDragging = true;
                const rect = node.getBoundingClientRect();
                const canvasRect = canvas.getBoundingClientRect();
                
                dragOffset.x = e.clientX - rect.left;
                dragOffset.y = e.clientY - rect.top;
                
                node.classList.add('dragging');
                e.stopPropagation();
            });
            
            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    const canvasRect = canvas.getBoundingClientRect();
                    const x = (e.clientX - canvasRect.left - dragOffset.x - pan.x) / zoom;
                    const y = (e.clientY - canvasRect.top - dragOffset.y - pan.y) / zoom;
                    
                    node.style.left = Math.max(0, x) + 'px';
                    node.style.top = Math.max(0, y) + 'px';
                }
            });
            
            document.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    node.classList.remove('dragging');
                }
            });
        }
        
        // Utility functions
        function updateTransform() {
            canvas.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;
        }
        
        function updateZoomDisplay() {
            document.getElementById('zoom-display').textContent = Math.round(zoom * 100) + '%';
        }
        
        function zoomIn() {
            zoom = Math.min(zoom * 1.2, 3);
            updateTransform();
            updateZoomDisplay();
        }
        
        function zoomOut() {
            zoom = Math.max(zoom * 0.8, 0.2);
            updateTransform();
            updateZoomDisplay();
        }
        
        function resetView() {
            zoom = 1;
            pan = { x: 0, y: 0 };
            updateTransform();
            updateZoomDisplay();
        }
        
        function clearCanvas() {
            const nodes = canvas.querySelectorAll('.workflow-node');
            nodes.forEach(node => node.remove());
            nodeCounter = 0;
        }
        
        function showError(message) {
            const error = document.createElement('div');
            error.className = 'error';
            error.textContent = message;
            nodeList.appendChild(error);
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
        
        // Initial setup
        updateZoomDisplay();
    </script>
</body>
</html>
