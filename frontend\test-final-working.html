<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Working Test - Dynamic Workflow</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        .header {
            background: #2a2a2a;
            padding: 10px 20px;
            border-bottom: 1px solid #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .sidebar {
            width: 250px;
            background: #2a2a2a;
            border-right: 1px solid #333;
            padding: 20px;
        }
        
        .canvas-area {
            flex: 1;
            position: relative;
            background: #333;
            overflow: hidden;
        }
        
        .canvas-controls {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #5aa3f0;
        }
        
        .zoom-display {
            background: rgba(0,0,0,0.7);
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .workflow-canvas {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .nodes-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transform-origin: 0 0;
        }
        
        .workflow-node {
            position: absolute;
            background: #4a90e2;
            border: 2px solid #5aa3f0;
            border-radius: 8px;
            padding: 10px;
            cursor: move;
            user-select: none;
            min-width: 120px;
            min-height: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .workflow-node:hover {
            background: #5aa3f0;
        }
        
        .workflow-node.dragging {
            opacity: 0.8;
            z-index: 1000;
        }
        
        .node-icon {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .node-title {
            font-weight: bold;
            font-size: 12px;
            text-align: center;
        }
        
        .status {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .node-item {
            background: #3a3a3a;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .node-item:hover {
            background: #4a4a4a;
        }
        
        .node-item-icon {
            font-size: 20px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>🎯 Final Working Test - 拖动和缩放</h1>
            <div class="zoom-display" id="zoomDisplay">100%</div>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <h3>节点面板</h3>
                <div class="node-item" onclick="createNode()">
                    <span class="node-item-icon">🔀</span>
                    <span>条件判断节点</span>
                </div>
                <div class="node-item" onclick="createNode2()">
                    <span class="node-item-icon">⚙️</span>
                    <span>处理节点</span>
                </div>
            </div>
            
            <div class="canvas-area">
                <div class="canvas-controls">
                    <button class="btn" onclick="zoomIn()">放大 (+)</button>
                    <button class="btn" onclick="zoomOut()">缩小 (-)</button>
                    <button class="btn" onclick="resetZoom()">重置</button>
                    <button class="btn" onclick="clearNodes()">清除</button>
                </div>
                
                <div class="workflow-canvas" id="canvas">
                    <div class="nodes-overlay" id="nodesOverlay"></div>
                </div>
                
                <div class="status" id="status">准备就绪 - 点击左侧节点创建，然后拖动测试</div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let zoom = 1;
        let pan = { x: 0, y: 0 };
        let nodeCounter = 0;
        let isDragging = false;
        let dragTarget = null;
        let dragOffset = { x: 0, y: 0 };

        const canvas = document.getElementById('canvas');
        const nodesOverlay = document.getElementById('nodesOverlay');
        const status = document.getElementById('status');
        const zoomDisplay = document.getElementById('zoomDisplay');

        function log(message) {
            status.textContent = message;
            console.log('🎯 Final Test:', message);
        }

        function updateTransform() {
            // 应用缩放和平移变换
            const transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;
            nodesOverlay.style.transform = transform;
            zoomDisplay.textContent = Math.round(zoom * 100) + '%';
            
            console.log('Transform update:', { zoom, pan, transform });
        }

        function createNode() {
            nodeCounter++;
            const node = document.createElement('div');
            node.className = 'workflow-node';
            node.innerHTML = `
                <div class="node-icon">🔀</div>
                <div class="node-title">条件节点 ${nodeCounter}</div>
            `;
            
            // 设置初始位置
            node.style.left = (100 + nodeCounter * 40) + 'px';
            node.style.top = (100 + nodeCounter * 40) + 'px';
            
            // 添加拖动功能
            setupNodeDragging(node);
            
            nodesOverlay.appendChild(node);
            log(`创建了条件节点 ${nodeCounter}`);
        }

        function createNode2() {
            nodeCounter++;
            const node = document.createElement('div');
            node.className = 'workflow-node';
            node.innerHTML = `
                <div class="node-icon">⚙️</div>
                <div class="node-title">处理节点 ${nodeCounter}</div>
            `;
            
            node.style.left = (200 + nodeCounter * 40) + 'px';
            node.style.top = (150 + nodeCounter * 40) + 'px';
            
            setupNodeDragging(node);
            nodesOverlay.appendChild(node);
            log(`创建了处理节点 ${nodeCounter}`);
        }

        function setupNodeDragging(node) {
            node.addEventListener('mousedown', (e) => {
                isDragging = true;
                dragTarget = node;
                
                const rect = node.getBoundingClientRect();
                const canvasRect = canvas.getBoundingClientRect();
                
                // 计算拖动偏移，考虑缩放
                dragOffset.x = (e.clientX - rect.left) / zoom;
                dragOffset.y = (e.clientY - rect.top) / zoom;
                
                node.classList.add('dragging');
                log(`开始拖动: ${node.querySelector('.node-title').textContent}`);
                
                e.preventDefault();
                e.stopPropagation();
            });
        }

        function updateDrag(e) {
            if (isDragging && dragTarget) {
                const canvasRect = canvas.getBoundingClientRect();
                
                // 计算新位置，考虑缩放和平移
                const x = (e.clientX - canvasRect.left - pan.x) / zoom - dragOffset.x;
                const y = (e.clientY - canvasRect.top - pan.y) / zoom - dragOffset.y;
                
                // 限制在画布范围内
                const clampedX = Math.max(0, Math.min(x, (canvas.clientWidth / zoom) - 120));
                const clampedY = Math.max(0, Math.min(y, (canvas.clientHeight / zoom) - 60));
                
                dragTarget.style.left = clampedX + 'px';
                dragTarget.style.top = clampedY + 'px';
                
                log(`拖动到: (${Math.round(clampedX)}, ${Math.round(clampedY)}) 缩放: ${Math.round(zoom * 100)}%`);
            }
        }

        function endDrag() {
            if (isDragging && dragTarget) {
                dragTarget.classList.remove('dragging');
                log(`拖动结束: ${dragTarget.querySelector('.node-title').textContent}`);
                isDragging = false;
                dragTarget = null;
            }
        }

        function zoomIn() {
            zoom = Math.min(zoom * 1.2, 3);
            updateTransform();
            log(`放大到: ${Math.round(zoom * 100)}%`);
        }

        function zoomOut() {
            zoom = Math.max(zoom * 0.8, 0.2);
            updateTransform();
            log(`缩小到: ${Math.round(zoom * 100)}%`);
        }

        function resetZoom() {
            zoom = 1;
            pan = { x: 0, y: 0 };
            updateTransform();
            log('重置缩放和位置');
        }

        function clearNodes() {
            nodesOverlay.innerHTML = '';
            nodeCounter = 0;
            log('清除所有节点');
        }

        // 事件监听器
        document.addEventListener('mousemove', updateDrag);
        document.addEventListener('mouseup', endDrag);

        // 鼠标滚轮缩放
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            zoom = Math.max(0.2, Math.min(3, zoom * delta));
            updateTransform();
            log(`滚轮缩放到: ${Math.round(zoom * 100)}%`);
        });

        // 初始化
        updateTransform();
        log('🎯 Final Working Test 已加载 - 功能完全正常！');
    </script>
</body>
</html>
