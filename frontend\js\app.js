/**
 * Dynamic Workflow System - Main Application
 * Initializes and coordinates all components of the workflow system
 */

class DynamicWorkflowApp {
  constructor() {
    this.components = {};
    this.state = {
      isInitialized: false,
      connectionStatus: 'connecting',
      currentWorkflow: null,
      selectedNodes: new Set(),
      clipboard: null
    };

    this.eventEmitter = new EventEmitter();
    this.apiClient = new ApiClient();

    this.init();
  }

  async init() {
    try {
      console.log('🚀 Initializing Dynamic Workflow System...');

      // Update status
      this.updateConnectionStatus('connecting');

      // Initialize core components
      await this.initializeComponents();

      // Setup event listeners
      this.setupEventListeners();

      // Load available nodes
      await this.loadAvailableNodes();

      // Setup keyboard shortcuts
      this.setupKeyboardShortcuts();

      // Mark as initialized
      this.state.isInitialized = true;
      this.updateConnectionStatus('connected');

      console.log('✅ Dynamic Workflow System initialized successfully');

      // Make app globally available for debugging
      window.app = this;

      // Emit ready event
      this.eventEmitter.emit('app:ready');

      // Run initial diagnostic
      if (window.workflowDebugger) {
        setTimeout(() => window.workflowDebugger.runFullDiagnostic(), 1000);
      }

    } catch (error) {
      console.error('❌ Failed to initialize application:', error);
      this.updateConnectionStatus('disconnected');
      this.showError('Failed to initialize application', error.message);
    }
  }

  async initializeComponents() {
    // Initialize API client
    this.apiClient.setBaseURL(window.location.origin);

    // Initialize workflow engine
    this.components.workflowEngine = new WorkflowEngine(this.eventEmitter);

    // Initialize node manager
    this.components.nodeManager = new NodeManager(this.eventEmitter);

    // Initialize connection manager
    this.components.connectionManager = new ConnectionManager(this.eventEmitter);

    // Initialize UI components
    this.components.workflowCanvas = new WorkflowCanvas(
      document.getElementById('workflow-canvas'),
      this.eventEmitter
    );

    this.components.nodePalette = new NodePalette(
      document.getElementById('node-categories'),
      this.eventEmitter
    );

    this.components.propertiesPanel = new PropertiesPanel(
      document.getElementById('properties-panel'),
      this.eventEmitter
    );

    // Set NodeManager reference in PropertiesPanel
    this.components.propertiesPanel.setNodeManager(this.components.nodeManager);

    this.components.nodeRenderer = new NodeRenderer(this.eventEmitter);

    // Set up node renderer container and canvas reference
    const nodesOverlay = document.getElementById('nodes-overlay');
    if (nodesOverlay) {
      this.components.nodeRenderer.setContainer(nodesOverlay);
      this.components.nodeRenderer.setCanvas(this.components.workflowCanvas);
    } else {
      console.error('nodes-overlay element not found');
    }

    // Initialize UI utilities
    this.components.dragDrop = new DragDropManager(this.eventEmitter);
    this.components.contextMenu = new ContextMenu(this.eventEmitter);
    this.components.modals = new ModalManager();

    console.log('✅ All components initialized');
  }

  setupEventListeners() {
    // Application-level events
    this.eventEmitter.on('app:error', (error) => {
      this.showError('Application Error', error.message);
    });

    this.eventEmitter.on('app:warning', (warning) => {
      this.showWarning(warning.message);
    });

    this.eventEmitter.on('app:info', (info) => {
      this.showInfo(info.message);
    });

    // Node events
    this.eventEmitter.on('node:clicked', (nodeId) => {
      console.log('App: node:clicked event received for nodeId:', nodeId);
      this.state.selectedNodes.clear();
      this.state.selectedNodes.add(nodeId);
      this.updateSelectionUI();
      this.eventEmitter.emit('node:select', nodeId);
    });

    this.eventEmitter.on('node:selected', (nodeId) => {
      this.state.selectedNodes.clear();
      this.state.selectedNodes.add(nodeId);
      this.updateSelectionUI();
    });

    this.eventEmitter.on('node:deselected', () => {
      this.state.selectedNodes.clear();
      this.updateSelectionUI();
    });

    this.eventEmitter.on('nodes:multiselect', (nodeIds) => {
      this.state.selectedNodes = new Set(nodeIds);
      this.updateSelectionUI();
    });

    this.eventEmitter.on('node:deselect:all', () => {
      this.state.selectedNodes.clear();
      this.updateSelectionUI();
      this.eventEmitter.emit('selection:changed', []);
    });

    // Node creation success notification
    this.eventEmitter.on('node:create', (nodeData) => {
      this.eventEmitter.emit('workflow:changed');
      console.log(`Created node: ${nodeData.name} at (${nodeData.position.x}, ${nodeData.position.y})`);
    });

    // Node get request (for properties panel)
    this.eventEmitter.on('node:get:request', (nodeId, callback) => {
      const node = this.components.nodeManager.getNode(nodeId);
      if (callback) callback(node);
    });

    // Node configuration updates
    this.eventEmitter.on('node:config:update', (data) => {
      this.components.nodeManager.updateNodeConfig(data.nodeId, data.config);
    });

    // Workflow events
    this.eventEmitter.on('workflow:changed', () => {
      this.updateWorkflowStats();
    });

    this.eventEmitter.on('workflow:executed', (results) => {
      this.handleWorkflowExecution(results);
    });

    // Setup button event listeners
    this.setupButtonListeners();

    console.log('✅ Event listeners setup complete');
  }

  setupButtonListeners() {
    // Workflow control buttons
    document.getElementById('btn-new-workflow').addEventListener('click', () => {
      this.newWorkflow();
    });

    document.getElementById('btn-save-workflow').addEventListener('click', () => {
      this.saveWorkflow();
    });

    document.getElementById('btn-load-workflow').addEventListener('click', () => {
      this.loadWorkflow();
    });

    document.getElementById('btn-execute-workflow').addEventListener('click', () => {
      this.executeWorkflow();
    });

    // Canvas control buttons
    document.getElementById('btn-zoom-in').addEventListener('click', () => {
      this.components.workflowCanvas.zoomIn();
    });

    document.getElementById('btn-zoom-out').addEventListener('click', () => {
      this.components.workflowCanvas.zoomOut();
    });

    document.getElementById('btn-zoom-fit').addEventListener('click', () => {
      this.components.workflowCanvas.zoomToFit();
    });

    document.getElementById('btn-grid-toggle').addEventListener('click', (e) => {
      this.toggleGrid(e.target);
    });

    document.getElementById('btn-snap-toggle').addEventListener('click', (e) => {
      this.toggleSnap(e.target);
    });

    // Properties panel close button
    document.getElementById('btn-close-properties').addEventListener('click', () => {
      this.components.propertiesPanel.hide();
    });
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + N - New workflow
      if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        this.newWorkflow();
      }

      // Ctrl/Cmd + S - Save workflow
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        this.saveWorkflow();
      }

      // Ctrl/Cmd + O - Open workflow
      if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
        e.preventDefault();
        this.loadWorkflow();
      }

      // Delete - Delete selected nodes
      if (e.key === 'Delete' && this.state.selectedNodes.size > 0) {
        this.deleteSelectedNodes();
      }

      // Ctrl/Cmd + C - Copy selected nodes
      if ((e.ctrlKey || e.metaKey) && e.key === 'c' && this.state.selectedNodes.size > 0) {
        e.preventDefault();
        this.copySelectedNodes();
      }

      // Ctrl/Cmd + V - Paste nodes
      if ((e.ctrlKey || e.metaKey) && e.key === 'v' && this.state.clipboard) {
        e.preventDefault();
        this.pasteNodes();
      }

      // Escape - Clear selection
      if (e.key === 'Escape') {
        this.clearSelection();
      }

      // F5 - Execute workflow
      if (e.key === 'F5') {
        e.preventDefault();
        this.executeWorkflow();
      }
    });
  }

  async loadAvailableNodes() {
    try {
      const nodes = await this.apiClient.get('/api/nodes');
      this.components.nodePalette.loadNodes(nodes);
      this.components.nodeManager.setAvailableNodes(nodes);
      console.log(`✅ Loaded ${nodes.length} node types`);
    } catch (error) {
      console.error('Failed to load available nodes:', error);
      this.showError('Failed to load node types', error.message);
    }
  }

  // Workflow Management Methods
  newWorkflow() {
    if (this.hasUnsavedChanges()) {
      this.components.modals.showConfirm(
        'Unsaved Changes',
        'You have unsaved changes. Are you sure you want to create a new workflow?',
        () => {
          this.createNewWorkflow();
        }
      );
    } else {
      this.createNewWorkflow();
    }
  }

  createNewWorkflow() {
    this.state.currentWorkflow = {
      id: this.generateId(),
      name: 'Untitled Workflow',
      nodes: [],
      connections: [],
      metadata: {
        created: new Date().toISOString(),
        modified: new Date().toISOString()
      }
    };

    this.components.workflowCanvas.clear();
    this.components.nodeManager.clear();
    this.components.connectionManager.clear();
    this.clearSelection();

    this.eventEmitter.emit('workflow:new', this.state.currentWorkflow);
    this.updateWorkflowStats();

    console.log('✅ New workflow created');
  }

  async saveWorkflow() {
    if (!this.state.currentWorkflow) {
      this.showWarning('No workflow to save');
      return;
    }

    try {
      // Collect current workflow state
      const workflowData = {
        ...this.state.currentWorkflow,
        nodes: this.components.nodeManager.getAllNodes(),
        connections: this.components.connectionManager.getAllConnections(),
        metadata: {
          ...this.state.currentWorkflow.metadata,
          modified: new Date().toISOString()
        }
      };

      // Save to local storage for now (could be extended to save to server)
      const storage = new WorkflowStorage();
      await storage.saveWorkflow(workflowData);

      this.state.currentWorkflow = workflowData;
      this.showInfo('Workflow saved successfully');

      console.log('✅ Workflow saved');
    } catch (error) {
      console.error('Failed to save workflow:', error);
      this.showError('Failed to save workflow', error.message);
    }
  }

  async loadWorkflow() {
    try {
      const storage = new WorkflowStorage();
      const workflows = await storage.getWorkflowList();

      if (workflows.length === 0) {
        this.showInfo('No saved workflows found');
        return;
      }

      // Show workflow selection modal
      this.components.modals.showWorkflowSelector(workflows, async (selectedWorkflow) => {
        await this.loadWorkflowData(selectedWorkflow);
      });

    } catch (error) {
      console.error('Failed to load workflows:', error);
      this.showError('Failed to load workflows', error.message);
    }
  }

  async loadWorkflowData(workflowData) {
    try {
      // Clear current workflow
      this.createNewWorkflow();

      // Load workflow data
      this.state.currentWorkflow = workflowData;

      // Restore nodes
      for (const nodeData of workflowData.nodes) {
        await this.components.nodeManager.createNode(nodeData);
      }

      // Restore connections
      for (const connectionData of workflowData.connections) {
        this.components.connectionManager.createConnection(connectionData);
      }

      // Update UI
      this.components.workflowCanvas.zoomToFit();
      this.updateWorkflowStats();

      this.showInfo(`Workflow "${workflowData.name}" loaded successfully`);
      console.log('✅ Workflow loaded');

    } catch (error) {
      console.error('Failed to load workflow data:', error);
      this.showError('Failed to load workflow', error.message);
    }
  }

  async executeWorkflow() {
    if (!this.state.currentWorkflow || this.state.currentWorkflow.nodes.length === 0) {
      this.showWarning('No workflow to execute');
      return;
    }

    try {
      this.updateExecutionStatus('Executing workflow...');

      const workflowData = {
        nodes: this.components.nodeManager.getAllNodes(),
        connections: this.components.connectionManager.getAllConnections()
      };

      const results = await this.apiClient.post('/api/workflow/execute', {
        workflow: workflowData,
        inputs: {} // Could be extended to accept user inputs
      });

      this.handleWorkflowExecution(results);

    } catch (error) {
      console.error('Workflow execution failed:', error);
      this.showError('Workflow execution failed', error.message);
      this.updateExecutionStatus('Execution failed');
    }
  }

  handleWorkflowExecution(results) {
    console.log('Workflow execution results:', results);
    this.updateExecutionStatus('Execution completed');

    // Show results in a modal or panel
    this.components.modals.showExecutionResults(results);

    this.eventEmitter.emit('workflow:executed', results);
  }

  // UI Update Methods
  updateConnectionStatus(status) {
    this.state.connectionStatus = status;

    const statusDot = document.getElementById('connection-status');
    const statusText = document.getElementById('status-text');

    statusDot.className = `status-dot status-${status}`;

    switch (status) {
      case 'connecting':
        statusText.textContent = 'Connecting...';
        break;
      case 'connected':
        statusText.textContent = 'Connected';
        break;
      case 'disconnected':
        statusText.textContent = 'Disconnected';
        break;
    }
  }

  updateWorkflowStats() {
    const nodeCount = this.components.nodeManager ? this.components.nodeManager.getNodeCount() : 0;
    const connectionCount = this.components.connectionManager ? this.components.connectionManager.getConnectionCount() : 0;

    document.getElementById('node-count').textContent = `${nodeCount} nodes`;
    document.getElementById('connection-count').textContent = `${connectionCount} connections`;
  }

  updateSelectionUI() {
    // Update properties panel based on selection
    if (this.state.selectedNodes.size === 1) {
      const nodeId = Array.from(this.state.selectedNodes)[0];
      console.log('App: updateSelectionUI - showing properties for nodeId:', nodeId);
      this.components.propertiesPanel.showNodeProperties(nodeId);
    } else if (this.state.selectedNodes.size > 1) {
      this.components.propertiesPanel.showMultiSelection(Array.from(this.state.selectedNodes));
    } else {
      this.components.propertiesPanel.hide();
    }
  }

  updateExecutionStatus(status) {
    document.getElementById('execution-status').querySelector('.status-text').textContent = status;
  }

  // Canvas Control Methods
  toggleGrid(button) {
    const isActive = button.classList.contains('active');
    if (isActive) {
      button.classList.remove('active');
      this.components.workflowCanvas.hideGrid();
    } else {
      button.classList.add('active');
      this.components.workflowCanvas.showGrid();
    }
  }

  toggleSnap(button) {
    const isActive = button.classList.contains('active');
    if (isActive) {
      button.classList.remove('active');
      this.components.workflowCanvas.disableSnap();
    } else {
      button.classList.add('active');
      this.components.workflowCanvas.enableSnap();
    }
  }

  // Node Management Methods
  deleteSelectedNodes() {
    if (this.state.selectedNodes.size === 0) return;

    this.components.modals.showConfirm(
      'Delete Nodes',
      `Are you sure you want to delete ${this.state.selectedNodes.size} node(s)?`,
      () => {
        for (const nodeId of this.state.selectedNodes) {
          this.components.nodeManager.deleteNode(nodeId);
        }
        this.clearSelection();
        this.eventEmitter.emit('workflow:changed');
      }
    );
  }

  copySelectedNodes() {
    if (this.state.selectedNodes.size === 0) return;

    const nodesToCopy = [];
    for (const nodeId of this.state.selectedNodes) {
      const node = this.components.nodeManager.getNode(nodeId);
      if (node) {
        nodesToCopy.push(node);
      }
    }

    this.state.clipboard = {
      type: 'nodes',
      data: nodesToCopy,
      timestamp: Date.now()
    };

    this.showInfo(`Copied ${nodesToCopy.length} node(s) to clipboard`);
  }

  pasteNodes() {
    if (!this.state.clipboard || this.state.clipboard.type !== 'nodes') return;

    const offset = { x: 50, y: 50 };
    const newNodeIds = [];

    for (const nodeData of this.state.clipboard.data) {
      const newNode = {
        ...nodeData,
        id: this.generateId(),
        position: {
          x: nodeData.position.x + offset.x,
          y: nodeData.position.y + offset.y
        }
      };

      this.components.nodeManager.createNode(newNode);
      newNodeIds.push(newNode.id);
    }

    // Select the pasted nodes
    this.state.selectedNodes = new Set(newNodeIds);
    this.updateSelectionUI();

    this.showInfo(`Pasted ${newNodeIds.length} node(s)`);
    this.eventEmitter.emit('workflow:changed');
  }

  clearSelection() {
    this.state.selectedNodes.clear();
    this.updateSelectionUI();
    this.eventEmitter.emit('selection:cleared');
  }



  // Utility Methods
  generateId() {
    return 'node_' + Math.random().toString(36).substr(2, 9);
  }

  hasUnsavedChanges() {
    // Simple implementation - could be more sophisticated
    return this.state.currentWorkflow &&
           this.components.nodeManager &&
           this.components.nodeManager.getNodeCount() > 0;
  }

  // Notification Methods
  showError(title, message) {
    console.error(`${title}: ${message}`);
    if (this.components && this.components.modals) {
      this.components.modals.showError(title, message);
    } else {
      alert(`${title}: ${message}`);
    }
  }

  showWarning(message) {
    console.warn(message);
    this.components.modals.showWarning('Warning', message);
  }

  showInfo(message) {
    console.info(message);
    this.components.modals.showInfo('Info', message);
  }

  // Public API for external access
  getComponent(name) {
    return this.components[name];
  }

  getState() {
    return { ...this.state };
  }

  addEventListener(event, callback) {
    this.eventEmitter.on(event, callback);
  }

  removeEventListener(event, callback) {
    this.eventEmitter.off(event, callback);
  }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Make app globally accessible for debugging
  window.workflowApp = new DynamicWorkflowApp();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DynamicWorkflowApp;
}