﻿/* Workflow Canvas Styles */

.workflow-canvas {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #fafafa;
    cursor: grab;
}

.workflow-canvas:active {
    cursor: grabbing;
}

.workflow-canvas.drag-over {
    background: rgba(33, 150, 243, 0.05);
}

.canvas-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.nodes-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-origin: 0 0;
}

/* Connection styles */
.connection-path {
    cursor: pointer;
    transition: stroke-width 0.2s ease;
}

.connection-path:hover {
    stroke-width: 3px !important;
}

.connection-selected {
    stroke: #FF9800 !important;
    stroke-width: 3px !important;
}

.data-flow-dot {
    pointer-events: none;
}

/* Grid pattern */
#grid path {
    stroke: #e0e0e0;
    stroke-width: 1;
}

/* Node highlighting */
.connection-highlighted {
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.5) !important;
}
