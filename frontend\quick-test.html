<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试 - 验证修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-links {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 20px 0;
        }
        .test-link {
            display: block;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            text-decoration: none;
            color: #495057;
            text-align: center;
            transition: all 0.2s ease;
        }
        .test-link:hover {
            background: #e9ecef;
            border-color: #007bff;
            color: #007bff;
        }
        .test-link.recommended {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-link.recommended:hover {
            background: #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 快速测试 - 验证修复</h1>
        
        <div class="status info">
            <strong>状态:</strong> <span id="status">正在检查...</span>
        </div>
        
        <div id="results"></div>
        
        <h2>📋 可用的测试页面</h2>
        <div class="test-links">
            <a href="standalone-workflow.html" class="test-link recommended">
                <strong>🎨 独立工作流编辑器</strong><br>
                <small>推荐 - 完整功能</small>
            </a>
            
            <a href="emergency-test.html" class="test-link">
                <strong>🚨 紧急诊断工具</strong><br>
                <small>已修复 - 系统诊断</small>
            </a>
            
            <a href="index-safe.html" class="test-link">
                <strong>🛡️ 安全模式</strong><br>
                <small>简化版本</small>
            </a>
            
            <a href="minimal-test.html" class="test-link">
                <strong>🔍 最小化测试</strong><br>
                <small>基础验证</small>
            </a>
            
            <a href="test-simple.html" class="test-link">
                <strong>⚡ 简单测试</strong><br>
                <small>最基础功能</small>
            </a>
            
            <a href="index.html" class="test-link">
                <strong>🏠 原始系统</strong><br>
                <small>可能仍有问题</small>
            </a>
        </div>
        
        <h2>🔧 快速操作</h2>
        <button onclick="testBasicFunctions()">测试基础功能</button>
        <button onclick="testServerConnection()">测试服务器连接</button>
        <button onclick="clearAllCache()">清除所有缓存</button>
        <button onclick="exportSystemInfo()">导出系统信息</button>
    </div>

    <script>
        // 状态更新
        function updateStatus(message, type = 'info') {
            document.getElementById('status').textContent = message;
            addResult(message, type);
        }
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }
        
        // 基础功能测试
        function testBasicFunctions() {
            try {
                // 测试JavaScript基础
                const testArray = [1, 2, 3];
                const testObject = { test: true };
                const testPromise = Promise.resolve('success');
                
                // 测试DOM操作
                const testDiv = document.createElement('div');
                testDiv.textContent = 'test';
                document.body.appendChild(testDiv);
                document.body.removeChild(testDiv);
                
                // 测试事件
                const testEvent = new CustomEvent('test');
                document.dispatchEvent(testEvent);
                
                // 测试存储
                localStorage.setItem('test', 'value');
                localStorage.removeItem('test');
                
                addResult('✅ 基础功能测试通过', 'success');
                updateStatus('基础功能正常');
                
            } catch (error) {
                addResult('❌ 基础功能测试失败: ' + error.message, 'error');
                updateStatus('基础功能异常');
            }
        }
        
        // 服务器连接测试
        async function testServerConnection() {
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ 服务器连接正常: ' + data.status, 'success');
                    updateStatus('服务器连接正常');
                } else {
                    addResult('❌ 服务器响应错误: ' + response.status, 'error');
                    updateStatus('服务器连接异常');
                }
            } catch (error) {
                addResult('❌ 服务器连接失败: ' + error.message, 'error');
                updateStatus('服务器连接失败');
            }
        }
        
        // 清除缓存
        function clearAllCache() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                addResult('✅ 缓存已清除', 'success');
                updateStatus('缓存清除完成');
            } catch (error) {
                addResult('❌ 清除缓存失败: ' + error.message, 'error');
            }
        }
        
        // 导出系统信息
        function exportSystemInfo() {
            const info = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                localStorage: Object.keys(localStorage),
                sessionStorage: Object.keys(sessionStorage),
                screen: {
                    width: screen.width,
                    height: screen.height
                },
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                }
            };
            
            const blob = new Blob([JSON.stringify(info, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-info-${new Date().toISOString().slice(0, 19)}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            addResult('✅ 系统信息已导出', 'success');
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('页面加载完成，准备就绪');
            
            // 自动运行基础测试
            setTimeout(() => {
                testBasicFunctions();
            }, 500);
            
            // 自动测试服务器连接
            setTimeout(() => {
                testServerConnection();
            }, 1000);
        });
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            addResult('全局错误: ' + event.message, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            addResult('Promise拒绝: ' + event.reason, 'error');
        });
        
        console.log('快速测试页面已加载');
    </script>
</body>
</html>
