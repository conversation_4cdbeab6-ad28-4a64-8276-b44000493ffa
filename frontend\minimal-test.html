<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Test - Dynamic Workflow System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 Dynamic Workflow System - 最小化测试</h1>
    
    <div class="test-section">
        <h2>基础环境测试</h2>
        <div id="basic-tests"></div>
    </div>
    
    <div class="test-section">
        <h2>服务器连接测试</h2>
        <button onclick="testServer()">测试服务器连接</button>
        <button onclick="testAPI()">测试API端点</button>
        <div id="server-tests"></div>
    </div>
    
    <div class="test-section">
        <h2>JavaScript文件测试</h2>
        <button onclick="testScripts()">测试脚本加载</button>
        <div id="script-tests"></div>
    </div>
    
    <div class="test-section">
        <h2>实时日志</h2>
        <button onclick="clearLog()">清除日志</button>
        <div id="log"></div>
    </div>

    <script>
        // 日志系统
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.innerHTML = `[${time}] ${type.toUpperCase()}: ${message}`;
            entry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function addStatus(containerId, message, type) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        // 基础环境测试
        function runBasicTests() {
            log('开始基础环境测试');
            
            // 测试JavaScript基础功能
            try {
                const testArray = [1, 2, 3];
                const testObject = { test: true };
                const testPromise = Promise.resolve('test');
                addStatus('basic-tests', '✅ JavaScript基础功能正常', 'success');
                log('JavaScript基础功能测试通过', 'success');
            } catch (error) {
                addStatus('basic-tests', '❌ JavaScript基础功能异常: ' + error.message, 'error');
                log('JavaScript基础功能测试失败: ' + error.message, 'error');
            }
            
            // 测试DOM操作
            try {
                const testDiv = document.createElement('div');
                testDiv.textContent = 'test';
                document.body.appendChild(testDiv);
                document.body.removeChild(testDiv);
                addStatus('basic-tests', '✅ DOM操作正常', 'success');
                log('DOM操作测试通过', 'success');
            } catch (error) {
                addStatus('basic-tests', '❌ DOM操作异常: ' + error.message, 'error');
                log('DOM操作测试失败: ' + error.message, 'error');
            }
            
            // 测试事件系统
            try {
                const testEvent = new CustomEvent('test');
                document.addEventListener('test', () => {});
                document.dispatchEvent(testEvent);
                addStatus('basic-tests', '✅ 事件系统正常', 'success');
                log('事件系统测试通过', 'success');
            } catch (error) {
                addStatus('basic-tests', '❌ 事件系统异常: ' + error.message, 'error');
                log('事件系统测试失败: ' + error.message, 'error');
            }
            
            // 测试本地存储
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                addStatus('basic-tests', '✅ 本地存储正常', 'success');
                log('本地存储测试通过', 'success');
            } catch (error) {
                addStatus('basic-tests', '❌ 本地存储异常: ' + error.message, 'error');
                log('本地存储测试失败: ' + error.message, 'error');
            }
        }
        
        // 服务器连接测试
        async function testServer() {
            log('开始服务器连接测试');
            const container = document.getElementById('server-tests');
            container.innerHTML = '';
            
            try {
                const response = await fetch('/api/health', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addStatus('server-tests', '✅ 服务器连接正常: ' + data.status, 'success');
                    log('服务器连接测试通过: ' + JSON.stringify(data), 'success');
                } else {
                    addStatus('server-tests', '❌ 服务器响应错误: ' + response.status, 'error');
                    log('服务器响应错误: ' + response.status + ' ' + response.statusText, 'error');
                }
            } catch (error) {
                addStatus('server-tests', '❌ 服务器连接失败: ' + error.message, 'error');
                log('服务器连接失败: ' + error.message, 'error');
            }
        }
        
        // API测试
        async function testAPI() {
            log('开始API端点测试');
            const container = document.getElementById('server-tests');
            
            try {
                log('正在请求 /api/nodes...');
                const response = await fetch('/api/nodes', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    const nodes = await response.json();
                    addStatus('server-tests', `✅ API正常，获取到 ${nodes.length} 个节点`, 'success');
                    log(`API测试通过，节点数量: ${nodes.length}`, 'success');
                    
                    // 显示节点信息
                    nodes.forEach((node, index) => {
                        log(`节点 ${index + 1}: ${node.name} (${node.id})`, 'info');
                    });
                } else {
                    addStatus('server-tests', '❌ API响应错误: ' + response.status, 'error');
                    log('API响应错误: ' + response.status + ' ' + response.statusText, 'error');
                }
            } catch (error) {
                addStatus('server-tests', '❌ API请求失败: ' + error.message, 'error');
                log('API请求失败: ' + error.message, 'error');
            }
        }
        
        // 脚本测试
        function testScripts() {
            log('开始JavaScript文件测试');
            const container = document.getElementById('script-tests');
            container.innerHTML = '';
            
            const scripts = [
                'js/utils/eventEmitter.js',
                'js/utils/apiClient.js',
                'js/utils/storage.js',
                'js/core/workflowEngine.js',
                'js/core/nodeManager.js',
                'js/components/workflowCanvas.js',
                'js/components/nodePalette.js',
                'js/app.js'
            ];
            
            scripts.forEach(scriptPath => {
                const script = document.createElement('script');
                script.src = scriptPath;
                script.onload = () => {
                    addStatus('script-tests', '✅ ' + scriptPath + ' 加载成功', 'success');
                    log(scriptPath + ' 加载成功', 'success');
                };
                script.onerror = () => {
                    addStatus('script-tests', '❌ ' + scriptPath + ' 加载失败', 'error');
                    log(scriptPath + ' 加载失败', 'error');
                };
                
                // 不实际添加到页面，只测试加载
                log('测试加载: ' + scriptPath, 'info');
            });
        }
        
        // 页面加载完成后运行基础测试
        document.addEventListener('DOMContentLoaded', () => {
            log('页面DOM加载完成', 'success');
            runBasicTests();
        });
        
        // 全局错误捕获
        window.addEventListener('error', (event) => {
            log('全局错误: ' + event.message + ' 在 ' + event.filename + ':' + event.lineno, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log('未处理的Promise拒绝: ' + event.reason, 'error');
        });
        
        log('最小化测试页面初始化完成', 'success');
    </script>
</body>
</html>
