const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const port = 3002;

// Enable CORS for development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  next();
});

// Parse JSON bodies
app.use(express.json());

// Serve static files
app.use(express.static('frontend'));
app.use('/nodes', express.static('nodes'));
app.use('/schema', express.static('schema'));

// Simple API endpoints
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'Dynamic Workflow System Test Server'
  });
});

app.get('/api/nodes', (req, res) => {
  const nodes = [];

  try {
    const files = fs.readdirSync('./nodes');
    files.forEach(file => {
      if (file.endsWith('.json')) {
        const nodeData = JSON.parse(fs.readFileSync(`./nodes/${file}`, 'utf8'));
        nodes.push(nodeData);
      }
    });
    console.log(`Loaded ${nodes.length} node types`);
  } catch (error) {
    console.error('Error loading nodes:', error);
    return res.status(500).json({ error: 'Failed to load nodes' });
  }

  res.json(nodes);
});

// Workflow execution endpoint (simplified)
app.post('/api/workflow/execute', (req, res) => {
  try {
    const { workflow, inputs } = req.body;

    if (!workflow || !workflow.nodes) {
      return res.status(400).json({ error: 'Invalid workflow structure' });
    }

    // Simplified execution - just return success
    const results = {
      success: true,
      executedNodes: workflow.nodes.length,
      timestamp: new Date().toISOString()
    };

    res.json(results);
  } catch (error) {
    console.error('Workflow execution error:', error);
    res.status(500).json({ error: error.message });
  }
});

app.listen(port, () => {
  console.log(`🚀 Dynamic Workflow Test Server running on http://localhost:${port}`);
  console.log(`📊 Frontend: http://localhost:${port}`);
  console.log(`🔌 API Health: http://localhost:${port}/api/health`);
  console.log(`📦 Available Nodes: http://localhost:${port}/api/nodes`);
});