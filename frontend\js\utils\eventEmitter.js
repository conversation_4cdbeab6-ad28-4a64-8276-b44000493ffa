/**
 * Event Emitter for Dynamic Workflow System
 * Provides a simple pub/sub system for component communication
 */

class EventEmitter {
  constructor() {
    this.events = new Map();
    this.maxListeners = 100;
  }

  /**
   * Add an event listener
   * @param {string} event - Event name
   * @param {function} callback - Callback function
   * @param {object} options - Options (once, priority)
   */
  on(event, callback, options = {}) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const listeners = this.events.get(event);

    // Check max listeners
    if (listeners.length >= this.maxListeners) {
      console.warn(`Max listeners (${this.maxListeners}) exceeded for event: ${event}`);
    }

    const listener = {
      callback,
      once: options.once || false,
      priority: options.priority || 0,
      id: Math.random().toString(36).substr(2, 9)
    };

    listeners.push(listener);

    // Sort by priority (higher priority first)
    listeners.sort((a, b) => b.priority - a.priority);

    return listener.id;
  }

  /**
   * Add a one-time event listener
   * @param {string} event - Event name
   * @param {function} callback - Callback function
   */
  once(event, callback) {
    return this.on(event, callback, { once: true });
  }

  /**
   * Remove an event listener
   * @param {string} event - Event name
   * @param {function|string} callbackOrId - Callback function or listener ID
   */
  off(event, callbackOrId) {
    if (!this.events.has(event)) {
      return false;
    }

    const listeners = this.events.get(event);
    const index = listeners.findIndex(listener =>
      listener.callback === callbackOrId || listener.id === callbackOrId
    );

    if (index !== -1) {
      listeners.splice(index, 1);

      // Clean up empty event arrays
      if (listeners.length === 0) {
        this.events.delete(event);
      }

      return true;
    }

    return false;
  }

  /**
   * Remove all listeners for an event
   * @param {string} event - Event name
   */
  removeAllListeners(event) {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }

  /**
   * Emit an event
   * @param {string} event - Event name
   * @param {...any} args - Arguments to pass to listeners
   */
  emit(event, ...args) {
    if (!this.events.has(event)) {
      return false;
    }

    const listeners = this.events.get(event).slice(); // Copy to avoid issues with modifications during iteration
    let hasListeners = false;

    for (const listener of listeners) {
      try {
        listener.callback(...args);
        hasListeners = true;

        // Remove one-time listeners
        if (listener.once) {
          this.off(event, listener.id);
        }
      } catch (error) {
        console.error(`Error in event listener for '${event}':`, error);
      }
    }

    return hasListeners;
  }

  /**
   * Emit an event asynchronously
   * @param {string} event - Event name
   * @param {...any} args - Arguments to pass to listeners
   */
  async emitAsync(event, ...args) {
    if (!this.events.has(event)) {
      return false;
    }

    const listeners = this.events.get(event).slice();
    let hasListeners = false;

    for (const listener of listeners) {
      try {
        await listener.callback(...args);
        hasListeners = true;

        // Remove one-time listeners
        if (listener.once) {
          this.off(event, listener.id);
        }
      } catch (error) {
        console.error(`Error in async event listener for '${event}':`, error);
      }
    }

    return hasListeners;
  }

  /**
   * Get the number of listeners for an event
   * @param {string} event - Event name
   */
  listenerCount(event) {
    return this.events.has(event) ? this.events.get(event).length : 0;
  }

  /**
   * Get all event names
   */
  eventNames() {
    return Array.from(this.events.keys());
  }

  /**
   * Get all listeners for an event
   * @param {string} event - Event name
   */
  listeners(event) {
    return this.events.has(event) ?
      this.events.get(event).map(l => l.callback) : [];
  }

  /**
   * Set the maximum number of listeners per event
   * @param {number} max - Maximum number of listeners
   */
  setMaxListeners(max) {
    this.maxListeners = max;
  }

  /**
   * Create a promise that resolves when an event is emitted
   * @param {string} event - Event name
   * @param {number} timeout - Optional timeout in milliseconds
   */
  waitFor(event, timeout) {
    return new Promise((resolve, reject) => {
      let timeoutId;

      const cleanup = () => {
        this.off(event, listener);
        if (timeoutId) clearTimeout(timeoutId);
      };

      const listener = (...args) => {
        cleanup();
        resolve(args.length === 1 ? args[0] : args);
      };

      this.once(event, listener);

      if (timeout) {
        timeoutId = setTimeout(() => {
          cleanup();
          reject(new Error(`Timeout waiting for event: ${event}`));
        }, timeout);
      }
    });
  }

  /**
   * Create a namespaced event emitter
   * @param {string} namespace - Namespace prefix
   */
  namespace(namespace) {
    return {
      on: (event, callback, options) => this.on(`${namespace}:${event}`, callback, options),
      once: (event, callback) => this.once(`${namespace}:${event}`, callback),
      off: (event, callback) => this.off(`${namespace}:${event}`, callback),
      emit: (event, ...args) => this.emit(`${namespace}:${event}`, ...args),
      emitAsync: (event, ...args) => this.emitAsync(`${namespace}:${event}`, ...args)
    };
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EventEmitter;
}