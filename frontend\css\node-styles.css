﻿/* Node Styles for Dynamic Workflow System */

/* Base Node Styles */
.workflow-node {
    position: absolute;
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    box-shadow: var(--shadow);
    cursor: move;
    user-select: none;
    transition: all 0.2s ease;
    min-width: 120px;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.workflow-node:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-1px);
}

.workflow-node.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
}

.workflow-node.dragging {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    opacity: 0.9;
    cursor: grabbing !important;
    z-index: 1000;
    transition: none !important;
    transform: rotate(2deg) scale(1.02);
}

.workflow-node.executing {
    border-color: var(--warning-color);
    animation: pulse 1s infinite;
}

.workflow-node.error {
    border-color: var(--error-color);
    background: rgba(244, 67, 54, 0.1);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(255, 152, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0); }
}

/* Node Header */
.node-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-primary);
    border-radius: 6px 6px 0 0;
    min-height: 36px;
}

.node-icon {
    font-size: 18px;
    margin-right: 10px;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    flex-shrink: 0;
}

.node-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.node-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    margin-left: 8px;
}

.node-status.idle { background: var(--text-secondary); }
.node-status.running { background: var(--warning-color); }
.node-status.error { background: var(--error-color); }
.node-status.complete { background: var(--success-color); }

/* Node Body */
.node-body {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    position: relative;
}

.node-content {
    text-align: center;
    font-size: 11px;
    color: var(--text-secondary);
    line-height: 1.3;
}

/* Node Pins */
.node-pins {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.node-pin {
    position: absolute;
    width: 12px;
    height: 12px;
    border: 2px solid var(--bg-secondary);
    border-radius: 50%;
    background: var(--text-secondary);
    cursor: crosshair;
    pointer-events: all;
    transition: all 0.2s ease;
    z-index: 10;
}

.node-pin:hover {
    transform: scale(1.2);
    background: var(--primary-color);
}

.node-pin.connected {
    background: var(--success-color);
}

.node-pin.input {
    left: -6px;
}

.node-pin.output {
    right: -6px;
}

.node-pin.active {
    background: var(--warning-color);
    box-shadow: 0 0 8px rgba(255, 152, 0, 0.6);
}

/* Pin Labels */
.pin-label {
    position: absolute;
    font-size: 10px;
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 6px;
    border-radius: 3px;
    white-space: nowrap;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 20;
}

.node-pin:hover + .pin-label,
.pin-label.visible {
    opacity: 1;
}

.pin-label.input {
    left: 15px;
    transform: translateY(-50%);
}

.pin-label.output {
    right: 15px;
    transform: translateY(-50%);
}

/* Node Type Specific Styles */
.workflow-node.conditional {
    border-radius: 8px;
    border-color: #4CAF50;
    background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
    position: relative;
}

.workflow-node.conditional::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #4CAF50, #66BB6A);
    border-radius: 10px;
    z-index: -1;
}

.workflow-node.multi-branch {
    border-radius: 8px;
    border-color: #FF9800;
    background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
    position: relative;
}

.workflow-node.multi-branch::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #FF9800, #FFB74D);
    border-radius: 10px;
    z-index: -1;
}

.workflow-node.loop {
    border-radius: 8px;
    border-color: #2196F3;
    background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
    position: relative;
}

.workflow-node.loop::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #2196F3, #42A5F5);
    border-radius: 10px;
    z-index: -1;
}

.workflow-node.custom-task {
    border-radius: 8px;
    border-color: #9C27B0;
    background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%);
    position: relative;
}

.workflow-node.custom-task::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #9C27B0, #BA68C8);
    border-radius: 10px;
    z-index: -1;
}

/* Special Logic Gate Shapes */
.workflow-node.and-gate {
    border-radius: 8px 50px 50px 8px;
    border-color: #4CAF50;
    background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
}

.workflow-node.or-gate {
    border-radius: 50px 8px 8px 50px;
    border-color: #FF9800;
    background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
}

.workflow-node.not-gate {
    border-radius: 8px;
    border-color: #F44336;
    background: linear-gradient(135deg, #FFEBEE 0%, #FFCDD2 100%);
    position: relative;
}

.workflow-node.not-gate::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid #F44336;
    border-radius: 50%;
    background: white;
}

/* Node Categories */
.node-category {
    margin-bottom: 20px;
}

.node-category h4 {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10px;
    padding: 0 5px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 5px;
}

.node-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 4px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s ease;
    font-size: 11px;
}

.node-item:hover {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    transform: translateX(2px);
}

.node-item:active {
    cursor: grabbing;
}

.node-item .node-icon {
    margin-right: 8px;
    font-size: 14px;
}

.node-item .node-name {
    flex: 1;
    font-weight: 500;
}

.node-item .node-description {
    font-size: 10px;
    color: var(--text-secondary);
    margin-top: 2px;
}

/* Drag and Drop States */
.node-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.workflow-canvas.drag-over {
    background: rgba(33, 150, 243, 0.1);
}

.drop-zone {
    position: absolute;
    border: 2px dashed var(--primary-color);
    border-radius: 8px;
    background: rgba(33, 150, 243, 0.1);
    pointer-events: none;
}

/* Responsive Node Sizing */
@media (max-width: 768px) {
    .workflow-node {
        min-width: 100px;
        min-height: 50px;
    }

    .node-header {
        padding: 6px 10px;
        min-height: 30px;
    }

    .node-title {
        font-size: 11px;
    }

    .node-pin {
        width: 10px;
        height: 10px;
    }
}
