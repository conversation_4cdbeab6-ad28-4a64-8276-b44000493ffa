﻿/**
 * Connection Manager for Dynamic Workflow System
 * Manages connections between nodes and handles data flow
 */

class ConnectionManager {
  constructor(eventEmitter) {
    this.eventEmitter = eventEmitter;
    this.connections = new Map();
    this.connectionElements = new Map();
    this.dataQueue = new Map(); // For queuing data between nodes
    this.executionOrder = [];

    this.setupEventListeners();
  }

  setupEventListeners() {
    // Connection creation
    this.eventEmitter.on('connection:create', (connectionData) => {
      this.createConnection(connectionData);
    });

    // Connection deletion
    this.eventEmitter.on('connection:delete', (connectionId) => {
      this.deleteConnection(connectionId);
    });

    // Data transmission
    this.eventEmitter.on('node:output:data', (data) => {
      this.transmitData(data);
    });

    // Node deletion (cleanup connections)
    this.eventEmitter.on('node:delete', (nodeId) => {
      this.removeNodeConnections(nodeId);
    });
  }

  createConnection(connectionData) {
    // Validate connection
    if (!this.validateConnection(connectionData)) {
      console.error('Invalid connection data:', connectionData);
      return false;
    }

    // Check for existing connection
    const existingId = this.findExistingConnection(connectionData);
    if (existingId) {
      console.warn('Connection already exists:', existingId);
      return false;
    }

    // Store connection
    this.connections.set(connectionData.id, {
      ...connectionData,
      created: new Date().toISOString(),
      dataTransmitted: 0,
      lastTransmission: null,
      status: 'active'
    });

    // Create visual representation
    this.createConnectionElement(connectionData);

    // Update execution order
    this.updateExecutionOrder();

    console.log(`Created connection: ${connectionData.source.nodeId}:${connectionData.source.pinName} -> ${connectionData.target.nodeId}:${connectionData.target.pinName}`);

    this.eventEmitter.emit('connection:created', connectionData);
    return true;
  }

  validateConnection(connectionData) {
    // Check required fields
    if (!connectionData || typeof connectionData !== 'object') {
      console.warn('Connection data is not an object:', connectionData);
      return false;
    }

    if (!connectionData.id || !connectionData.source || !connectionData.target) {
      console.warn('Missing required connection fields:', connectionData);
      return false;
    }

    // Check source and target structure
    if (!connectionData.source.nodeId || !connectionData.source.pinName ||
        !connectionData.target.nodeId || !connectionData.target.pinName) {
      console.warn('Invalid source/target structure:', connectionData);
      return false;
    }

    // Can't connect node to itself
    if (connectionData.source.nodeId === connectionData.target.nodeId) {
      console.warn('Cannot connect node to itself:', connectionData);
      return false;
    }

    return true;
  }

  findExistingConnection(connectionData) {
    for (const [id, conn] of this.connections) {
      if (conn.source.nodeId === connectionData.source.nodeId &&
          conn.source.pinName === connectionData.source.pinName &&
          conn.target.nodeId === connectionData.target.nodeId &&
          conn.target.pinName === connectionData.target.pinName) {
        return id;
      }
    }
    return null;
  }

  createConnectionElement(connectionData) {
    // Get pin positions
    const sourcePos = this.getPinPosition(connectionData.source.nodeId, connectionData.source.pinName);
    const targetPos = this.getPinPosition(connectionData.target.nodeId, connectionData.target.pinName);

    if (!sourcePos || !targetPos) {
      console.error('Could not find pin positions for connection');
      return null;
    }

    // Create SVG path element
    const canvas = document.getElementById('canvas-svg');
    const connectionsLayer = canvas.querySelector('#connections-layer');

    if (!connectionsLayer) {
      console.error('Connections layer not found');
      return null;
    }

    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path.id = `connection-${connectionData.id}`;
    path.setAttribute('class', 'connection-path');
    path.setAttribute('stroke', '#666');
    path.setAttribute('stroke-width', '2');
    path.setAttribute('fill', 'none');
    path.setAttribute('stroke-linecap', 'round');

    // Create path data
    const pathData = this.createConnectionPath(sourcePos, targetPos);
    path.setAttribute('d', pathData);

    // Add interaction handlers
    this.setupConnectionInteractions(path, connectionData);

    connectionsLayer.appendChild(path);
    this.connectionElements.set(connectionData.id, path);

    return path;
  }

  getPinPosition(nodeId, pinName) {
    const nodeElement = document.getElementById(`node-${nodeId}`);
    if (!nodeElement) return null;

    const pinElement = nodeElement.querySelector(`[data-pin-name="${pinName}"]`);
    if (!pinElement) return null;

    const nodeRect = nodeElement.getBoundingClientRect();
    const pinRect = pinElement.getBoundingClientRect();
    const canvas = document.getElementById('workflow-canvas');
    const canvasRect = canvas.getBoundingClientRect();

    return {
      x: pinRect.left + pinRect.width / 2 - canvasRect.left,
      y: pinRect.top + pinRect.height / 2 - canvasRect.top
    };
  }

  createConnectionPath(start, end) {
    const dx = end.x - start.x;
    const dy = end.y - start.y;

    // Create smooth Bezier curve
    const cp1x = start.x + Math.abs(dx) * 0.5;
    const cp1y = start.y;
    const cp2x = end.x - Math.abs(dx) * 0.5;
    const cp2y = end.y;

    return `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`;
  }

  setupConnectionInteractions(pathElement, connectionData) {
    // Hover effects
    pathElement.addEventListener('mouseenter', () => {
      pathElement.setAttribute('stroke', '#2196F3');
      pathElement.setAttribute('stroke-width', '3');
      this.highlightConnectedNodes(connectionData);
    });

    pathElement.addEventListener('mouseleave', () => {
      pathElement.setAttribute('stroke', '#666');
      pathElement.setAttribute('stroke-width', '2');
      this.clearNodeHighlights();
    });

    // Click to select
    pathElement.addEventListener('click', (e) => {
      e.stopPropagation();
      this.selectConnection(connectionData.id);
    });

    // Context menu
    pathElement.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      this.eventEmitter.emit('connection:context-menu', {
        connectionId: connectionData.id,
        position: { x: e.clientX, y: e.clientY }
      });
    });

    // Double-click to delete
    pathElement.addEventListener('dblclick', () => {
      this.deleteConnection(connectionData.id);
    });
  }

  highlightConnectedNodes(connectionData) {
    const sourceNode = document.getElementById(`node-${connectionData.source.nodeId}`);
    const targetNode = document.getElementById(`node-${connectionData.target.nodeId}`);

    if (sourceNode) sourceNode.classList.add('connection-highlighted');
    if (targetNode) targetNode.classList.add('connection-highlighted');
  }

  clearNodeHighlights() {
    document.querySelectorAll('.connection-highlighted').forEach(node => {
      node.classList.remove('connection-highlighted');
    });
  }

  selectConnection(connectionId) {
    // Clear previous selection
    document.querySelectorAll('.connection-selected').forEach(conn => {
      conn.classList.remove('connection-selected');
    });

    // Select new connection
    const pathElement = this.connectionElements.get(connectionId);
    if (pathElement) {
      pathElement.classList.add('connection-selected');
      pathElement.setAttribute('stroke', '#FF9800');
    }

    this.eventEmitter.emit('connection:selected', connectionId);
  }

  deleteConnection(connectionId) {
    const connection = this.connections.get(connectionId);
    if (!connection) return false;

    // Remove visual element
    const pathElement = this.connectionElements.get(connectionId);
    if (pathElement) {
      pathElement.remove();
      this.connectionElements.delete(connectionId);
    }

    // Remove from storage
    this.connections.delete(connectionId);

    // Update execution order
    this.updateExecutionOrder();

    console.log(`Deleted connection: ${connectionId}`);
    this.eventEmitter.emit('connection:deleted', connectionId);

    return true;
  }

  removeNodeConnections(nodeId) {
    const connectionsToRemove = [];

    // Find all connections involving this node
    for (const [id, connection] of this.connections) {
      if (connection.source.nodeId === nodeId || connection.target.nodeId === nodeId) {
        connectionsToRemove.push(id);
      }
    }

    // Remove found connections
    connectionsToRemove.forEach(id => this.deleteConnection(id));
  }

  // Data transmission methods
  transmitData(outputData) {
    const { nodeId, pinName, data, timestamp } = outputData;

    // Find all connections from this output pin
    const outgoingConnections = this.getConnectionsFromPin(nodeId, pinName);

    if (outgoingConnections.length === 0) {
      console.log(`No connections from ${nodeId}:${pinName}`);
      return;
    }

    // Transmit data to all connected input pins
    outgoingConnections.forEach(connection => {
      this.sendDataThroughConnection(connection, data, timestamp);
    });
  }

  getConnectionsFromPin(nodeId, pinName) {
    return Array.from(this.connections.values()).filter(conn =>
      conn.source.nodeId === nodeId && conn.source.pinName === pinName
    );
  }

  getConnectionsToPin(nodeId, pinName) {
    return Array.from(this.connections.values()).filter(conn =>
      conn.target.nodeId === nodeId && conn.target.pinName === pinName
    );
  }

  sendDataThroughConnection(connection, data, timestamp) {
    // Update connection statistics
    connection.dataTransmitted++;
    connection.lastTransmission = timestamp || new Date().toISOString();

    // Animate connection
    this.animateConnection(connection.id);

    // Queue data for target node
    this.queueDataForNode(connection.target.nodeId, connection.target.pinName, data);

    // Emit data received event
    this.eventEmitter.emit('node:input:data', {
      nodeId: connection.target.nodeId,
      pinName: connection.target.pinName,
      data: data,
      sourceNodeId: connection.source.nodeId,
      sourcePinName: connection.source.pinName,
      connectionId: connection.id
    });
  }

  queueDataForNode(nodeId, pinName, data) {
    if (!this.dataQueue.has(nodeId)) {
      this.dataQueue.set(nodeId, new Map());
    }

    const nodeQueue = this.dataQueue.get(nodeId);
    if (!nodeQueue.has(pinName)) {
      nodeQueue.set(pinName, []);
    }

    nodeQueue.get(pinName).push({
      data: data,
      timestamp: new Date().toISOString()
    });

    // Trigger node execution check
    this.eventEmitter.emit('node:check:execution', nodeId);
  }

  getQueuedData(nodeId, pinName) {
    const nodeQueue = this.dataQueue.get(nodeId);
    if (!nodeQueue || !nodeQueue.has(pinName)) {
      return null;
    }

    const pinQueue = nodeQueue.get(pinName);
    return pinQueue.shift(); // FIFO
  }

  hasQueuedData(nodeId, pinName) {
    const nodeQueue = this.dataQueue.get(nodeId);
    if (!nodeQueue || !nodeQueue.has(pinName)) {
      return false;
    }

    return nodeQueue.get(pinName).length > 0;
  }

  clearNodeQueue(nodeId) {
    this.dataQueue.delete(nodeId);
  }

  animateConnection(connectionId) {
    const pathElement = this.connectionElements.get(connectionId);
    if (!pathElement) return;

    // Create animated dot
    const canvas = document.getElementById('canvas-svg');
    const connectionsLayer = canvas.querySelector('#connections-layer');

    const dot = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    dot.setAttribute('r', '4');
    dot.setAttribute('fill', '#2196F3');
    dot.setAttribute('class', 'data-flow-dot');

    // Animate along path
    const animateMotion = document.createElementNS('http://www.w3.org/2000/svg', 'animateMotion');
    animateMotion.setAttribute('dur', '1s');
    animateMotion.setAttribute('repeatCount', '1');

    const mpath = document.createElementNS('http://www.w3.org/2000/svg', 'mpath');
    mpath.setAttributeNS('http://www.w3.org/1999/xlink', 'href', `#connection-${connectionId}`);

    animateMotion.appendChild(mpath);
    dot.appendChild(animateMotion);
    connectionsLayer.appendChild(dot);

    // Remove dot after animation
    setTimeout(() => {
      if (dot.parentNode) {
        dot.parentNode.removeChild(dot);
      }
    }, 1000);
  }

  // Execution order management
  updateExecutionOrder() {
    this.executionOrder = this.calculateExecutionOrder();
    this.eventEmitter.emit('execution:order:updated', this.executionOrder);
  }

  calculateExecutionOrder() {
    // Topological sort to determine execution order
    const nodes = new Set();
    const inDegree = new Map();
    const graph = new Map();

    // Build graph from connections
    for (const connection of this.connections.values()) {
      const source = connection.source.nodeId;
      const target = connection.target.nodeId;

      nodes.add(source);
      nodes.add(target);

      if (!graph.has(source)) graph.set(source, []);
      graph.get(source).push(target);

      inDegree.set(target, (inDegree.get(target) || 0) + 1);
      if (!inDegree.has(source)) inDegree.set(source, 0);
    }

    // Kahn's algorithm for topological sorting
    const queue = [];
    const result = [];

    // Find nodes with no incoming edges
    for (const node of nodes) {
      if (inDegree.get(node) === 0) {
        queue.push(node);
      }
    }

    while (queue.length > 0) {
      const current = queue.shift();
      result.push(current);

      const neighbors = graph.get(current) || [];
      for (const neighbor of neighbors) {
        inDegree.set(neighbor, inDegree.get(neighbor) - 1);
        if (inDegree.get(neighbor) === 0) {
          queue.push(neighbor);
        }
      }
    }

    // Check for cycles
    if (result.length !== nodes.size) {
      console.warn('Cycle detected in workflow graph');
      return Array.from(nodes); // Return all nodes if cycle detected
    }

    return result;
  }

  getExecutionOrder() {
    return [...this.executionOrder];
  }

  // Utility methods
  getAllConnections() {
    return Array.from(this.connections.values());
  }

  getConnection(connectionId) {
    return this.connections.get(connectionId);
  }

  getConnectionCount() {
    return this.connections.size;
  }

  getNodeConnections(nodeId) {
    return Array.from(this.connections.values()).filter(conn =>
      conn.source.nodeId === nodeId || conn.target.nodeId === nodeId
    );
  }

  getNodeInputConnections(nodeId) {
    return Array.from(this.connections.values()).filter(conn =>
      conn.target.nodeId === nodeId
    );
  }

  getNodeOutputConnections(nodeId) {
    return Array.from(this.connections.values()).filter(conn =>
      conn.source.nodeId === nodeId
    );
  }

  updateConnectionPositions() {
    // Update all connection paths when nodes move
    for (const [id, connection] of this.connections) {
      const pathElement = this.connectionElements.get(id);
      if (!pathElement) continue;

      const sourcePos = this.getPinPosition(connection.source.nodeId, connection.source.pinName);
      const targetPos = this.getPinPosition(connection.target.nodeId, connection.target.pinName);

      if (sourcePos && targetPos) {
        const pathData = this.createConnectionPath(sourcePos, targetPos);
        pathElement.setAttribute('d', pathData);
      }
    }
  }

  clear() {
    // Clear all visual elements
    this.connectionElements.forEach(element => {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    });

    // Clear data structures
    this.connections.clear();
    this.connectionElements.clear();
    this.dataQueue.clear();
    this.executionOrder = [];
  }

  // Export/Import
  exportConnections() {
    return {
      connections: this.getAllConnections(),
      executionOrder: this.executionOrder,
      metadata: {
        connectionCount: this.getConnectionCount(),
        exportedAt: new Date().toISOString()
      }
    };
  }

  importConnections(data) {
    if (!data.connections || !Array.isArray(data.connections)) {
      throw new Error('Invalid connection import data');
    }

    const importedConnections = [];

    data.connections.forEach(connectionData => {
      try {
        if (this.createConnection(connectionData)) {
          importedConnections.push(connectionData);
        }
      } catch (error) {
        console.error('Failed to import connection:', error);
      }
    });

    return importedConnections;
  }

  // Debug methods
  debugConnections() {
    console.log('=== Connection Manager Debug ===');
    console.log('Connections:', this.connections.size);
    console.log('Execution Order:', this.executionOrder);
    console.log('Data Queue:', this.dataQueue);

    this.connections.forEach((conn, id) => {
      console.log(`${id}: ${conn.source.nodeId}:${conn.source.pinName} -> ${conn.target.nodeId}:${conn.target.pinName}`);
    });
  }
}
