const Ajv = require('ajv');
const fs = require('fs-extra');
const path = require('path');

class SchemaValidator {
  constructor() {
    this.ajv = new Ajv({ allErrors: true });
    this.nodeSchema = null;
    this.validateNodeConfig = null;
    this.initialized = false;
  }

  async loadSchema() {
    if (this.initialized) return;

    try {
      const schemaPath = path.join(__dirname, '../../schema/workflow-node-schema.json');
      this.nodeSchema = await fs.readJson(schemaPath);
      this.validateNodeConfig = this.ajv.compile(this.nodeSchema);
      this.initialized = true;
      console.log('✓ Schema validator initialized');
    } catch (error) {
      console.error('Error loading schema:', error);
      throw error;
    }
  }

  async validateNode(nodeConfig) {
    await this.loadSchema();

    if (!this.validateNodeConfig) {
      throw new Error('Schema validator not initialized');
    }

    const isValid = this.validateNodeConfig(nodeConfig);

    if (!isValid) {
      const errors = this.validateNodeConfig.errors.map(error => ({
        path: error.instancePath,
        message: error.message,
        value: error.data
      }));

      return {
        valid: false,
        errors: errors
      };
    }

    return { valid: true, errors: [] };
  }

  getSchema() {
    return this.nodeSchema;
  }
}

// Create singleton instance
const validator = new SchemaValidator();

// Initialize validator immediately
validator.loadSchema().catch(console.error);

module.exports = {
  validateNodeConfig: async (nodeConfig) => {
    const result = await validator.validateNode(nodeConfig);
    return result.valid;
  },
  validateNodeConfigWithErrors: async (nodeConfig) => {
    return await validator.validateNode(nodeConfig);
  },
  getSchema: () => validator.getSchema()
};