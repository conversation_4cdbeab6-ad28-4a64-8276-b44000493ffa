﻿/**
 * Node Manager for Dynamic Workflow System
 * Manages workflow nodes, their lifecycle, and interactions
 */

class NodeManager {
  constructor(eventEmitter) {
    this.eventEmitter = eventEmitter;
    this.nodes = new Map();
    this.availableNodes = [];
    this.selectedNodes = new Set();
    this.nodeCounter = 0;

    this.setupEventListeners();
  }

  setupEventListeners() {
    // Node creation requests
    this.eventEmitter.on('node:create:request', (data) => {
      this.createNodeFromType(data.nodeType, data.position);
    });

    this.eventEmitter.on('node:add:center', (data) => {
      const canvas = document.getElementById('workflow-canvas');
      const rect = canvas.getBoundingClientRect();

      // Calculate center position with offset for each new node
      const baseX = rect.width / 2 - 60; // Offset to center the node
      const baseY = rect.height / 2 - 40;
      const offset = this.nodeCounter * 30; // 30px offset for each new node

      const centerPosition = {
        x: baseX + offset,
        y: baseY + offset
      };

      console.log('NodeManager: Creating node at center position:', centerPosition);
      this.createNodeFromType(data.nodeType, centerPosition);
    });

    // Node selection
    this.eventEmitter.on('node:clicked', (nodeId) => {
      this.selectNode(nodeId);
    });

    this.eventEmitter.on('node:select:single', (nodeId) => {
      this.selectNode(nodeId, false);
    });

    this.eventEmitter.on('node:toggle:selection', (nodeId) => {
      this.toggleNodeSelection(nodeId);
    });

    this.eventEmitter.on('node:deselect:all', () => {
      this.clearSelection();
    });

    // Node position updates
    this.eventEmitter.on('node:position:updated', (data) => {
      if (data && data.nodeId && data.position) {
        this.updateNodePosition(data.nodeId, data.position);
      }
    });

    // Node deletion
    this.eventEmitter.on('node:delete:request', (nodeId) => {
      this.deleteNode(nodeId);
    });
  }

  setAvailableNodes(nodes) {
    this.availableNodes = nodes;
  }

  createNodeFromType(nodeTypeId, position) {
    const nodeTemplate = this.availableNodes.find(node => node.id === nodeTypeId);
    if (!nodeTemplate) {
      console.error(`Node type ${nodeTypeId} not found`);
      return null;
    }

    const nodeData = this.createNodeData(nodeTemplate, position);
    return this.createNode(nodeData);
  }

  createNodeData(template, position) {
    this.nodeCounter++;

    return {
      id: `node_${this.nodeCounter}_${Date.now()}`,
      type: template.type,
      templateId: template.id,
      name: template.name,
      description: template.description,
      position: position || { x: 100, y: 100 },

      // Copy template data
      pins: JSON.parse(JSON.stringify(template.pins)),
      visual: JSON.parse(JSON.stringify(template.visual)),
      metadata: JSON.parse(JSON.stringify(template.metadata)),

      // Runtime data
      status: 'idle',
      config: {},
      lastExecuted: null,
      executionCount: 0,

      // Frontend specific
      frontend: template.frontend,
      backend: template.backend
    };
  }

  createNode(nodeData) {
    // Validate node data
    if (!nodeData.id || this.nodes.has(nodeData.id)) {
      console.error('Invalid or duplicate node ID');
      return null;
    }

    // Store node
    this.nodes.set(nodeData.id, nodeData);

    // Emit creation event for renderer
    this.eventEmitter.emit('node:create', nodeData);

    console.log(`Created node: ${nodeData.name} (${nodeData.id})`);
    return nodeData;
  }

  updateNode(nodeId, updates) {
    const node = this.nodes.get(nodeId);
    if (!node) return false;

    // Apply updates
    Object.assign(node, updates);

    // Emit update event
    this.eventEmitter.emit('node:update', node);

    return true;
  }

  updateNodePosition(nodeId, position) {
    const node = this.nodes.get(nodeId);
    if (!node) return false;

    node.position = position;
    this.eventEmitter.emit('node:position:changed', { nodeId, position });

    return true;
  }

  updateNodeConfig(nodeId, config) {
    const node = this.nodes.get(nodeId);
    if (!node) return false;

    node.config = { ...node.config, ...config };
    this.eventEmitter.emit('node:config:changed', { nodeId, config: node.config });

    return true;
  }

  deleteNode(nodeId) {
    const node = this.nodes.get(nodeId);
    if (!node) return false;

    // Remove from selection if selected
    this.selectedNodes.delete(nodeId);

    // Remove from storage
    this.nodes.delete(nodeId);

    // Emit deletion event
    this.eventEmitter.emit('node:delete', nodeId);

    console.log(`Deleted node: ${node.name} (${nodeId})`);
    return true;
  }

  selectNode(nodeId, multiSelect = false) {
    if (!this.nodes.has(nodeId)) return false;

    if (!multiSelect) {
      // Clear previous selection
      this.selectedNodes.forEach(id => {
        this.eventEmitter.emit('node:deselect', id);
      });
      this.selectedNodes.clear();
    }

    // Add to selection
    this.selectedNodes.add(nodeId);
    this.eventEmitter.emit('node:select', nodeId);

    // Emit selection change
    this.eventEmitter.emit('selection:changed', Array.from(this.selectedNodes));

    return true;
  }

  toggleNodeSelection(nodeId) {
    if (!this.nodes.has(nodeId)) return false;

    if (this.selectedNodes.has(nodeId)) {
      this.selectedNodes.delete(nodeId);
      this.eventEmitter.emit('node:deselect', nodeId);
    } else {
      this.selectedNodes.add(nodeId);
      this.eventEmitter.emit('node:select', nodeId);
    }

    this.eventEmitter.emit('selection:changed', Array.from(this.selectedNodes));
    return true;
  }

  clearSelection() {
    this.selectedNodes.forEach(nodeId => {
      this.eventEmitter.emit('node:deselect', nodeId);
    });
    this.selectedNodes.clear();
    this.eventEmitter.emit('selection:changed', []);
  }

  getNode(nodeId) {
    return this.nodes.get(nodeId);
  }

  getAllNodes() {
    return Array.from(this.nodes.values());
  }

  getSelectedNodes() {
    return Array.from(this.selectedNodes).map(id => this.nodes.get(id)).filter(Boolean);
  }

  getNodesByType(type) {
    return this.getAllNodes().filter(node => node.type === type);
  }

  getNodeCount() {
    return this.nodes.size;
  }

  getSelectedCount() {
    return this.selectedNodes.size;
  }

  clear() {
    // Clear selection first
    this.clearSelection();

    // Delete all nodes
    const nodeIds = Array.from(this.nodes.keys());
    nodeIds.forEach(nodeId => {
      this.eventEmitter.emit('node:delete', nodeId);
    });

    this.nodes.clear();
    this.nodeCounter = 0;
  }

  // Node execution methods
  setNodeStatus(nodeId, status) {
    const node = this.nodes.get(nodeId);
    if (!node) return false;

    node.status = status;
    this.eventEmitter.emit('node:status:changed', { nodeId, status });

    return true;
  }

  markNodeExecuted(nodeId) {
    const node = this.nodes.get(nodeId);
    if (!node) return false;

    node.lastExecuted = new Date().toISOString();
    node.executionCount++;

    return true;
  }

  // Utility methods
  findNodeAt(position, tolerance = 10) {
    for (const node of this.nodes.values()) {
      const nodePos = node.position;
      const distance = Math.sqrt(
        Math.pow(position.x - nodePos.x, 2) +
        Math.pow(position.y - nodePos.y, 2)
      );

      if (distance <= tolerance) {
        return node;
      }
    }
    return null;
  }

  getNodesInArea(topLeft, bottomRight) {
    return this.getAllNodes().filter(node => {
      const pos = node.position;
      return pos.x >= topLeft.x && pos.x <= bottomRight.x &&
             pos.y >= topLeft.y && pos.y <= bottomRight.y;
    });
  }

  duplicateNode(nodeId, offset = { x: 50, y: 50 }) {
    const originalNode = this.nodes.get(nodeId);
    if (!originalNode) return null;

    const duplicatedData = {
      ...JSON.parse(JSON.stringify(originalNode)),
      id: undefined, // Will be generated
      position: {
        x: originalNode.position.x + offset.x,
        y: originalNode.position.y + offset.y
      }
    };

    return this.createNode(this.createNodeData(duplicatedData, duplicatedData.position));
  }

  exportNodes() {
    return {
      nodes: this.getAllNodes(),
      metadata: {
        nodeCount: this.getNodeCount(),
        exportedAt: new Date().toISOString()
      }
    };
  }

  importNodes(data) {
    if (!data.nodes || !Array.isArray(data.nodes)) {
      throw new Error('Invalid import data');
    }

    const importedNodes = [];

    data.nodes.forEach(nodeData => {
      try {
        const node = this.createNode(nodeData);
        if (node) {
          importedNodes.push(node);
        }
      } catch (error) {
        console.error('Failed to import node:', error);
      }
    });

    return importedNodes;
  }
}
