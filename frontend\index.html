<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Workflow System</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/workflow-canvas.css">
    <link rel="stylesheet" href="css/node-styles.css">
    <link rel="stylesheet" href="css/properties-panel.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS Variables -->
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #FFC107;
            --success-color: #4CAF50;
            --error-color: #F44336;
            --warning-color: #FF9800;
            --info-color: #00BCD4;

            --bg-primary: #f5f5f5;
            --bg-secondary: #ffffff;
            --bg-dark: #263238;

            --text-primary: #212121;
            --text-secondary: #757575;
            --text-light: #ffffff;

            --border-color: #e0e0e0;
            --shadow: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <!-- Main Application Container -->
    <div id="app" class="app-container">

        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <h1 class="app-title">
                    <i class="fas fa-project-diagram"></i>
                    Dynamic Workflow System
                </h1>
            </div>

            <div class="header-center">
                <div class="workflow-controls">
                    <button id="btn-new-workflow" class="btn btn-primary">
                        <i class="fas fa-plus"></i> New Workflow
                    </button>
                    <button id="btn-save-workflow" class="btn btn-success">
                        <i class="fas fa-save"></i> Save
                    </button>
                    <button id="btn-load-workflow" class="btn btn-info">
                        <i class="fas fa-folder-open"></i> Load
                    </button>
                    <button id="btn-execute-workflow" class="btn btn-warning">
                        <i class="fas fa-play"></i> Execute
                    </button>
                </div>
            </div>

            <div class="header-right">
                <div class="status-indicator">
                    <span id="connection-status" class="status-dot status-connecting"></span>
                    <span id="status-text">Connecting...</span>
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="app-main">

            <!-- Left Sidebar - Node Palette -->
            <aside class="sidebar sidebar-left">
                <div class="sidebar-header">
                    <h3>Node Palette</h3>
                    <div class="search-container">
                        <input type="text" id="node-search" placeholder="Search nodes..." class="search-input">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>

                <div class="sidebar-content">
                    <div id="node-categories" class="node-categories">
                        <!-- Node categories will be dynamically loaded here -->
                        <div class="category-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading nodes...
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Central Workflow Canvas -->
            <section class="workflow-canvas-container">
                <div class="canvas-toolbar">
                    <div class="toolbar-left">
                        <button id="btn-zoom-in" class="btn btn-sm">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button id="btn-zoom-out" class="btn btn-sm">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button id="btn-zoom-fit" class="btn btn-sm">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <span class="zoom-level">100%</span>
                    </div>

                    <div class="toolbar-center">
                        <span id="canvas-info" class="canvas-info">
                            Ready - Drop nodes here to build your workflow
                        </span>
                    </div>

                    <div class="toolbar-right">
                        <button id="btn-grid-toggle" class="btn btn-sm active">
                            <i class="fas fa-th"></i> Grid
                        </button>
                        <button id="btn-snap-toggle" class="btn btn-sm active">
                            <i class="fas fa-magnet"></i> Snap
                        </button>
                    </div>
                </div>

                <div id="workflow-canvas" class="workflow-canvas">
                    <svg id="canvas-svg" class="canvas-svg">
                        <!-- Grid pattern -->
                        <defs>
                            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#grid)" />

                        <!-- Connections layer -->
                        <g id="connections-layer" class="connections-layer"></g>

                        <!-- Nodes layer -->
                        <g id="nodes-layer" class="nodes-layer"></g>

                        <!-- Selection layer -->
                        <g id="selection-layer" class="selection-layer"></g>
                    </svg>

                    <!-- HTML overlay for node content -->
                    <div id="nodes-overlay" class="nodes-overlay"></div>
                </div>
            </section>

            <!-- Right Sidebar - Properties Panel -->
            <aside class="sidebar sidebar-right">
                <div class="sidebar-header">
                    <h3>Properties</h3>
                    <button id="btn-close-properties" class="btn btn-sm btn-ghost">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="sidebar-content">
                    <div id="properties-panel" class="properties-panel">
                        <div class="no-selection">
                            <i class="fas fa-mouse-pointer"></i>
                            <p>Select a node to view its properties</p>
                        </div>
                    </div>
                </div>
            </aside>

        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-left">
                <span id="node-count">0 nodes</span>
                <span class="separator">|</span>
                <span id="connection-count">0 connections</span>
            </div>

            <div class="footer-center">
                <div id="execution-status" class="execution-status">
                    <span class="status-text">Ready</span>
                </div>
            </div>

            <div class="footer-right">
                <span class="version">v1.0.0</span>
            </div>
        </footer>
    </div>

    <!-- Modal Dialogs -->
    <div id="modal-overlay" class="modal-overlay hidden">
        <div id="modal-container" class="modal-container">
            <!-- Modal content will be dynamically inserted here -->
        </div>
    </div>

    <!-- Context Menu -->
    <div id="context-menu" class="context-menu hidden">
        <!-- Context menu items will be dynamically inserted here -->
    </div>

    <!-- JavaScript Files -->
    <script src="js/debug/debugger.js"></script>
    <script src="js/utils/eventEmitter.js"></script>
    <script src="js/utils/apiClient.js"></script>
    <script src="js/utils/storage.js"></script>

    <script src="js/core/workflowEngine.js"></script>
    <script src="js/core/nodeManager.js"></script>
    <script src="js/core/connectionManager.js"></script>

    <script src="js/components/workflowCanvas.js"></script>
    <script src="js/components/nodePalette.js"></script>
    <script src="js/components/propertiesPanel.js"></script>
    <script src="js/components/nodeRenderer.js?v=1751892900"></script>

    <script src="js/ui/dragDrop.js"></script>
    <script src="js/ui/contextMenu.js"></script>
    <script src="js/ui/modals.js"></script>

    <script src="js/app.js?v=1751892900"></script>
</body>
</html>