<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Workflow System - Simple Test</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #ddd;
            padding: 20px;
        }
        
        .canvas-area {
            flex: 1;
            position: relative;
            background: #fafafa;
            overflow: hidden;
        }
        
        .node-item {
            padding: 10px;
            margin: 5px 0;
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            cursor: move;
            user-select: none;
        }
        
        .node-item:hover {
            background: #bbdefb;
        }
        
        .test-node {
            position: absolute;
            width: 120px;
            height: 60px;
            background: white;
            border: 2px solid #2196f3;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: move;
            user-select: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-node.dragging {
            opacity: 0.8;
            transform: rotate(2deg);
            z-index: 1000;
        }
        
        .canvas {
            width: 100%;
            height: 100%;
            position: relative;
            transform-origin: 0 0;
        }
        
        .controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        button {
            margin: 2px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 3px;
        }
        
        button:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h3>Node Palette</h3>
            <div class="node-item" draggable="true" data-node-type="test">
                🔧 Test Node
            </div>
            <div class="node-item" draggable="true" data-node-type="condition">
                🔀 Condition Node
            </div>
            <div class="node-item" draggable="true" data-node-type="loop">
                🔄 Loop Node
            </div>
        </div>
        
        <div class="canvas-area">
            <div class="controls">
                <button onclick="zoomIn()">Zoom In</button>
                <button onclick="zoomOut()">Zoom Out</button>
                <button onclick="resetZoom()">Reset</button>
                <span id="zoom-display">100%</span>
            </div>
            
            <div class="canvas" id="canvas">
                <!-- Nodes will be added here -->
            </div>
        </div>
    </div>

    <script>
        let zoom = 1;
        let pan = { x: 0, y: 0 };
        let nodeCounter = 0;
        let isDraggingCanvas = false;
        let dragStart = { x: 0, y: 0 };
        
        const canvas = document.getElementById('canvas');
        
        // Drag and drop from palette
        document.querySelectorAll('.node-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/node-type', item.dataset.nodeType);
            });
        });
        
        // Canvas drop handling
        canvas.addEventListener('dragover', (e) => {
            e.preventDefault();
        });
        
        canvas.addEventListener('drop', (e) => {
            e.preventDefault();
            const nodeType = e.dataTransfer.getData('text/node-type');
            if (nodeType) {
                createNode(nodeType, e.clientX - canvas.getBoundingClientRect().left, e.clientY - canvas.getBoundingClientRect().top);
            }
        });
        
        // Canvas panning
        canvas.addEventListener('mousedown', (e) => {
            if (e.target === canvas) {
                isDraggingCanvas = true;
                dragStart = { x: e.clientX - pan.x, y: e.clientY - pan.y };
                canvas.style.cursor = 'grabbing';
            }
        });
        
        document.addEventListener('mousemove', (e) => {
            if (isDraggingCanvas) {
                pan.x = e.clientX - dragStart.x;
                pan.y = e.clientY - dragStart.y;
                updateTransform();
            }
        });
        
        document.addEventListener('mouseup', () => {
            isDraggingCanvas = false;
            canvas.style.cursor = '';
        });
        
        // Zoom functions
        function zoomIn() {
            zoom = Math.min(zoom * 1.2, 3);
            updateTransform();
            updateZoomDisplay();
        }
        
        function zoomOut() {
            zoom = Math.max(zoom * 0.8, 0.2);
            updateTransform();
            updateZoomDisplay();
        }
        
        function resetZoom() {
            zoom = 1;
            pan = { x: 0, y: 0 };
            updateTransform();
            updateZoomDisplay();
        }
        
        function updateTransform() {
            canvas.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;
        }
        
        function updateZoomDisplay() {
            document.getElementById('zoom-display').textContent = Math.round(zoom * 100) + '%';
        }
        
        // Create node function
        function createNode(type, x, y) {
            nodeCounter++;
            const node = document.createElement('div');
            node.className = 'test-node';
            node.textContent = type + ' ' + nodeCounter;
            node.style.left = (x / zoom - pan.x / zoom) + 'px';
            node.style.top = (y / zoom - pan.y / zoom) + 'px';
            
            // Node dragging
            let isDragging = false;
            let dragOffset = { x: 0, y: 0 };
            
            node.addEventListener('mousedown', (e) => {
                isDragging = true;
                const rect = node.getBoundingClientRect();
                dragOffset.x = e.clientX - rect.left;
                dragOffset.y = e.clientY - rect.top;
                node.classList.add('dragging');
                e.stopPropagation();
            });
            
            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    const canvasRect = canvas.getBoundingClientRect();
                    const x = (e.clientX - canvasRect.left - dragOffset.x) / zoom - pan.x / zoom;
                    const y = (e.clientY - canvasRect.top - dragOffset.y) / zoom - pan.y / zoom;
                    node.style.left = x + 'px';
                    node.style.top = y + 'px';
                }
            });
            
            document.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    node.classList.remove('dragging');
                }
            });
            
            canvas.appendChild(node);
        }
        
        // Initial setup
        updateZoomDisplay();
    </script>
</body>
</html>
