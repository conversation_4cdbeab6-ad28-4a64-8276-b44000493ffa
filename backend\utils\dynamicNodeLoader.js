const fs = require('fs-extra');
const path = require('path');
const { validateNodeConfigWithErrors } = require('./schemaValidator');

class DynamicNodeLoader {
  constructor() {
    this.loadedNodes = new Map();
    this.watchedFiles = new Map();
    this.nodesDirectory = path.join(__dirname, '../../nodes');
  }

  async loadAllNodes() {
    try {
      const nodeFiles = await fs.readdir(this.nodesDirectory);
      const loadPromises = [];

      for (const file of nodeFiles) {
        if (file.endsWith('.json')) {
          loadPromises.push(this.loadNode(file));
        }
      }

      const results = await Promise.allSettled(loadPromises);

      let successCount = 0;
      let errorCount = 0;

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          successCount++;
        } else {
          errorCount++;
          console.error(`Failed to load node file ${nodeFiles[index]}:`, result.reason);
        }
      });

      console.log(`Node loading complete: ${successCount} successful, ${errorCount} failed`);
      return this.loadedNodes;
    } catch (error) {
      console.error('Error loading nodes directory:', error);
      throw error;
    }
  }

  async loadNode(filename) {
    try {
      const filePath = path.join(this.nodesDirectory, filename);
      const nodeConfig = await fs.readJson(filePath);

      // Validate the node configuration
      const validation = validateNodeConfigWithErrors(nodeConfig);
      if (!validation.valid) {
        throw new Error(`Invalid node configuration: ${JSON.stringify(validation.errors, null, 2)}`);
      }

      // Process and enhance the node configuration
      const processedNode = this.processNodeConfig(nodeConfig);

      // Store the loaded node
      this.loadedNodes.set(processedNode.id, processedNode);

      console.log(`✓ Loaded node: ${processedNode.name} (${processedNode.id})`);
      return processedNode;
    } catch (error) {
      console.error(`Error loading node from ${filename}:`, error.message);
      throw error;
    }
  }

  processNodeConfig(nodeConfig) {
    // Clone the configuration to avoid mutations
    const processed = JSON.parse(JSON.stringify(nodeConfig));

    // Add runtime metadata
    processed.runtime = {
      loadedAt: new Date().toISOString(),
      version: processed.version || '1.0.0'
    };

    // Validate and process backend code
    if (processed.backend && processed.backend.code) {
      processed.backend.compiledFunction = this.compileBackendFunction(processed.backend.code);
    }

    // Validate and process frontend code
    if (processed.frontend && processed.frontend.executable && processed.frontend.executable.code) {
      processed.frontend.executable.validated = this.validateFrontendCode(processed.frontend.executable.code);
    }

    // Process pin definitions
    processed.pins = this.processPinDefinitions(processed.pins);

    return processed;
  }

  compileBackendFunction(code) {
    try {
      // Validate that the code can be compiled
      new Function('req', 'res', 'next', code);
      return true;
    } catch (error) {
      console.warn('Backend code compilation warning:', error.message);
      return false;
    }
  }

  validateFrontendCode(code) {
    try {
      // Basic validation - check for class definition
      if (!code.includes('class ') || !code.includes('constructor')) {
        throw new Error('Frontend code must contain a class with constructor');
      }

      // Check for required methods
      const requiredMethods = ['execute', 'receiveInput', 'sendOutput'];
      for (const method of requiredMethods) {
        if (!code.includes(method)) {
          console.warn(`Frontend code missing recommended method: ${method}`);
        }
      }

      return true;
    } catch (error) {
      console.warn('Frontend code validation warning:', error.message);
      return false;
    }
  }

  processPinDefinitions(pins) {
    const processed = { ...pins };

    // Ensure pin definitions have unique names
    ['input', 'output'].forEach(pinType => {
      if (processed[pinType] && processed[pinType].definitions) {
        const names = new Set();
        processed[pinType].definitions.forEach(pin => {
          if (names.has(pin.name)) {
            throw new Error(`Duplicate pin name '${pin.name}' in ${pinType} pins`);
          }
          names.add(pin.name);
        });
      }
    });

    return processed;
  }

  getNode(nodeId) {
    return this.loadedNodes.get(nodeId);
  }

  getAllNodes() {
    return Array.from(this.loadedNodes.values());
  }

  getNodesByType(type) {
    return this.getAllNodes().filter(node => node.type === type);
  }

  getNodesByCategory(category) {
    return this.getAllNodes().filter(node =>
      node.metadata && node.metadata.category === category
    );
  }

  async reloadNode(nodeId) {
    const node = this.loadedNodes.get(nodeId);
    if (!node) {
      throw new Error(`Node ${nodeId} not found`);
    }

    // Find the original file
    const nodeFiles = await fs.readdir(this.nodesDirectory);
    const nodeFile = nodeFiles.find(file => {
      const filePath = path.join(this.nodesDirectory, file);
      try {
        const config = require(filePath);
        return config.id === nodeId;
      } catch {
        return false;
      }
    });

    if (!nodeFile) {
      throw new Error(`Source file for node ${nodeId} not found`);
    }

    // Reload the node
    await this.loadNode(nodeFile);
    console.log(`✓ Reloaded node: ${nodeId}`);
  }

  getLoadedNodeIds() {
    return Array.from(this.loadedNodes.keys());
  }

  getNodeCount() {
    return this.loadedNodes.size;
  }
}

module.exports = DynamicNodeLoader;