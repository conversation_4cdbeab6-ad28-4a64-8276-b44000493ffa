﻿/**
 * Drag and Drop Manager for Dynamic Workflow System
 * Handles all drag and drop interactions including node creation and connections
 */

class DragDropManager {
  constructor(eventEmitter) {
    this.eventEmitter = eventEmitter;
    this.isDragging = false;
    this.dragType = null; // 'node', 'connection', 'selection'
    this.dragData = null;
    this.dragElement = null;
    this.connectionPreview = null;
    this.dropZones = [];

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.createConnectionPreview();
  }

  setupEventListeners() {
    // Global drag events
    document.addEventListener('dragstart', (e) => this.handleDragStart(e));
    document.addEventListener('dragover', (e) => this.handleDragOver(e));
    document.addEventListener('drop', (e) => this.handleDrop(e));
    document.addEventListener('dragend', (e) => this.handleDragEnd(e));

    // Pin connection events
    this.eventEmitter.on('pin:connection:start', (data) => {
      this.startConnectionDrag(data);
    });

    // Node drag events from renderer
    this.eventEmitter.on('node:drag:start', (data) => {
      this.startNodeDrag(data);
    });

    this.eventEmitter.on('node:drag:move', (data) => {
      this.updateNodeDrag(data);
    });

    this.eventEmitter.on('node:drag:end', (data) => {
      this.endNodeDrag(data);
    });
  }

  handleDragStart(e) {
    const nodeType = e.dataTransfer.getData('text/node-type');
    if (nodeType) {
      this.dragType = 'node-creation';
      this.dragData = { nodeType };
      this.isDragging = true;

      // Create drag preview
      this.createDragPreview(e.target);
    }
  }

  handleDragOver(e) {
    e.preventDefault();

    if (this.dragType === 'node-creation') {
      this.updateDropZones(e);
    }
  }

  handleDrop(e) {
    e.preventDefault();

    if (this.dragType === 'node-creation') {
      this.handleNodeDrop(e);
    }

    this.clearDropZones();
  }

  handleDragEnd(e) {
    this.isDragging = false;
    this.dragType = null;
    this.dragData = null;
    this.clearDragPreview();
    this.clearDropZones();
  }

  handleNodeDrop(e) {
    const canvas = document.getElementById('workflow-canvas');
    if (!canvas.contains(e.target) && e.target !== canvas) {
      return; // Not dropped on canvas
    }

    const canvasRect = canvas.getBoundingClientRect();
    const position = {
      x: e.clientX - canvasRect.left,
      y: e.clientY - canvasRect.top
    };

    this.eventEmitter.emit('node:create:request', {
      nodeType: this.dragData.nodeType,
      position: position
    });
  }

  startConnectionDrag(data) {
    this.dragType = 'connection';
    this.dragData = data;
    this.isDragging = true;

    // Show connection preview
    this.showConnectionPreview(data.position);

    // Highlight compatible pins
    this.eventEmitter.emit('pins:highlight:compatible', {
      sourcePin: data
    });

    // Setup mouse tracking for connection preview
    this.setupConnectionTracking();
  }

  setupConnectionTracking() {
    const mouseMoveHandler = (e) => {
      if (this.dragType === 'connection') {
        this.updateConnectionPreview({
          x: e.clientX,
          y: e.clientY
        });
      }
    };

    const mouseUpHandler = (e) => {
      if (this.dragType === 'connection') {
        this.endConnectionDrag(e);
      }
      document.removeEventListener('mousemove', mouseMoveHandler);
      document.removeEventListener('mouseup', mouseUpHandler);
    };

    document.addEventListener('mousemove', mouseMoveHandler);
    document.addEventListener('mouseup', mouseUpHandler);
  }

  endConnectionDrag(e) {
    // Check if dropped on a compatible pin
    const targetPin = this.findPinUnderMouse(e);

    if (targetPin && this.isCompatiblePin(this.dragData, targetPin)) {
      this.createConnection(this.dragData, targetPin);
    }

    this.hideConnectionPreview();
    this.eventEmitter.emit('pins:clear:highlights');

    this.isDragging = false;
    this.dragType = null;
    this.dragData = null;
  }

  findPinUnderMouse(e) {
    const elements = document.elementsFromPoint(e.clientX, e.clientY);
    const pinElement = elements.find(el => el.classList.contains('node-pin'));

    if (pinElement) {
      return {
        element: pinElement,
        pinName: pinElement.dataset.pinName,
        pinType: pinElement.dataset.pinType,
        dataType: pinElement.dataset.dataType,
        nodeId: pinElement.closest('.workflow-node').id.replace('node-', '')
      };
    }

    return null;
  }

  isCompatiblePin(sourcePin, targetPin) {
    // Can't connect to same node
    if (sourcePin.nodeId === targetPin.nodeId) return false;

    // Must be different pin types (input to output or vice versa)
    if (sourcePin.pinType === targetPin.pinType) return false;

    // Check data type compatibility
    const sourceType = sourcePin.dataType;
    const targetType = targetPin.dataType;

    return sourceType === 'any' || targetType === 'any' || sourceType === targetType;
  }

  createConnection(sourcePin, targetPin) {
    const connectionData = {
      id: this.generateConnectionId(),
      source: {
        nodeId: sourcePin.nodeId,
        pinName: sourcePin.pinName
      },
      target: {
        nodeId: targetPin.nodeId,
        pinName: targetPin.pinName
      },
      dataType: sourcePin.dataType === 'any' ? targetPin.dataType : sourcePin.dataType
    };

    this.eventEmitter.emit('connection:create', connectionData);
  }

  startNodeDrag(data) {
    this.dragType = 'node-move';
    this.dragData = data;
    this.isDragging = true;
  }

  updateNodeDrag(data) {
    // Node position is already updated by the node renderer
    // We can add additional logic here if needed
  }

  endNodeDrag(data) {
    this.isDragging = false;
    this.dragType = null;
    this.dragData = null;

    // Emit final position update only if we have valid data
    if (data && data.nodeId && data.position) {
      this.eventEmitter.emit('node:position:updated', data);
    }
  }

  createDragPreview(sourceElement) {
    this.dragElement = sourceElement.cloneNode(true);
    this.dragElement.classList.add('drag-preview');
    this.dragElement.style.position = 'fixed';
    this.dragElement.style.pointerEvents = 'none';
    this.dragElement.style.zIndex = '10000';
    this.dragElement.style.opacity = '0.8';
    this.dragElement.style.transform = 'rotate(5deg)';

    document.body.appendChild(this.dragElement);
  }

  clearDragPreview() {
    if (this.dragElement) {
      this.dragElement.remove();
      this.dragElement = null;
    }
  }

  createConnectionPreview() {
    this.connectionPreview = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    this.connectionPreview.setAttribute('stroke', '#2196F3');
    this.connectionPreview.setAttribute('stroke-width', '2');
    this.connectionPreview.setAttribute('fill', 'none');
    this.connectionPreview.setAttribute('stroke-dasharray', '5,5');
    this.connectionPreview.style.display = 'none';
    this.connectionPreview.style.pointerEvents = 'none';

    // Add to canvas SVG
    const canvas = document.getElementById('canvas-svg');
    const connectionsLayer = canvas.querySelector('#connections-layer');
    if (connectionsLayer) {
      connectionsLayer.appendChild(this.connectionPreview);
    }
  }

  showConnectionPreview(startPosition) {
    this.connectionPreview.style.display = 'block';
    this.connectionStartPos = startPosition;
    this.updateConnectionPreview(startPosition);
  }

  updateConnectionPreview(currentPosition) {
    if (!this.connectionPreview || !this.connectionStartPos) return;

    const canvas = document.getElementById('workflow-canvas');
    const canvasRect = canvas.getBoundingClientRect();

    // Convert screen coordinates to canvas coordinates
    const start = {
      x: this.connectionStartPos.x - canvasRect.left,
      y: this.connectionStartPos.y - canvasRect.top
    };

    const end = {
      x: currentPosition.x - canvasRect.left,
      y: currentPosition.y - canvasRect.top
    };

    // Create smooth curve path
    const pathData = this.createConnectionPath(start, end);
    this.connectionPreview.setAttribute('d', pathData);
  }

  hideConnectionPreview() {
    if (this.connectionPreview) {
      this.connectionPreview.style.display = 'none';
    }
    this.connectionStartPos = null;
  }

  createConnectionPath(start, end) {
    const dx = end.x - start.x;
    const dy = end.y - start.y;

    // Create a smooth Bezier curve
    const cp1x = start.x + Math.abs(dx) * 0.5;
    const cp1y = start.y;
    const cp2x = end.x - Math.abs(dx) * 0.5;
    const cp2y = end.y;

    return `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`;
  }

  updateDropZones(e) {
    const canvas = document.getElementById('workflow-canvas');
    if (!canvas) return;

    // Highlight canvas as drop zone
    if (this.isOverElement(e, canvas)) {
      canvas.classList.add('drag-over');
    } else {
      canvas.classList.remove('drag-over');
    }
  }

  clearDropZones() {
    const canvas = document.getElementById('workflow-canvas');
    if (canvas) {
      canvas.classList.remove('drag-over');
    }
  }

  isOverElement(e, element) {
    const rect = element.getBoundingClientRect();
    return (
      e.clientX >= rect.left &&
      e.clientX <= rect.right &&
      e.clientY >= rect.top &&
      e.clientY <= rect.bottom
    );
  }

  generateConnectionId() {
    return 'conn_' + Math.random().toString(36).substr(2, 9);
  }

  // Selection rectangle drag
  startSelectionDrag(startPosition) {
    this.dragType = 'selection';
    this.dragData = { startPosition };
    this.isDragging = true;

    this.eventEmitter.emit('selection:start', startPosition);
  }

  updateSelectionDrag(currentPosition) {
    if (this.dragType !== 'selection') return;

    this.eventEmitter.emit('selection:update', {
      start: this.dragData.startPosition,
      current: currentPosition
    });
  }

  endSelectionDrag(endPosition) {
    if (this.dragType !== 'selection') return;

    this.eventEmitter.emit('selection:end', {
      start: this.dragData.startPosition,
      end: endPosition
    });

    this.isDragging = false;
    this.dragType = null;
    this.dragData = null;
  }

  // Multi-selection with Ctrl/Cmd
  handleMultiSelection(nodeId, isCtrlPressed) {
    if (isCtrlPressed) {
      this.eventEmitter.emit('node:toggle:selection', nodeId);
    } else {
      this.eventEmitter.emit('node:select:single', nodeId);
    }
  }

  // Utility methods
  isDraggingNode() {
    return this.dragType === 'node-move';
  }

  isDraggingConnection() {
    return this.dragType === 'connection';
  }

  isDraggingSelection() {
    return this.dragType === 'selection';
  }

  getCurrentDragType() {
    return this.dragType;
  }

  getCurrentDragData() {
    return this.dragData;
  }

  // Snap to grid functionality
  snapToGrid(position, gridSize = 20) {
    return {
      x: Math.round(position.x / gridSize) * gridSize,
      y: Math.round(position.y / gridSize) * gridSize
    };
  }

  // Distance calculation for snap detection
  getDistance(pos1, pos2) {
    const dx = pos2.x - pos1.x;
    const dy = pos2.y - pos1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  // Find nearest snap point
  findNearestSnapPoint(position, snapPoints, threshold = 10) {
    let nearest = null;
    let minDistance = threshold;

    snapPoints.forEach(point => {
      const distance = this.getDistance(position, point);
      if (distance < minDistance) {
        minDistance = distance;
        nearest = point;
      }
    });

    return nearest;
  }

  // Enable/disable drag and drop
  enable() {
    this.enabled = true;
  }

  disable() {
    this.enabled = false;
    this.cancelCurrentDrag();
  }

  cancelCurrentDrag() {
    if (this.isDragging) {
      this.hideConnectionPreview();
      this.clearDragPreview();
      this.clearDropZones();
      this.eventEmitter.emit('pins:clear:highlights');

      this.isDragging = false;
      this.dragType = null;
      this.dragData = null;
    }
  }

  // Cleanup
  destroy() {
    this.cancelCurrentDrag();

    if (this.connectionPreview) {
      this.connectionPreview.remove();
    }
  }
}
