﻿/**
 * Node Palette Component
 * Displays available node types and handles drag-and-drop to canvas
 */

class NodePalette {
  constructor(element, eventEmitter) {
    this.element = element;
    this.eventEmitter = eventEmitter;
    this.availableNodes = [];
    this.filteredNodes = [];
    this.searchInput = document.getElementById('node-search');

    this.setupEventListeners();
  }

  setupEventListeners() {
    // Search functionality
    if (this.searchInput) {
      this.searchInput.addEventListener('input', (e) => {
        this.filterNodes(e.target.value);
      });
    }
  }

  loadNodes(nodes) {
    this.availableNodes = nodes;
    this.filteredNodes = [...nodes];
    this.render();
  }

  filterNodes(searchTerm) {
    const term = searchTerm.toLowerCase();
    this.filteredNodes = this.availableNodes.filter(node =>
      node.name.toLowerCase().includes(term) ||
      node.description.toLowerCase().includes(term) ||
      node.metadata.tags.some(tag => tag.toLowerCase().includes(term))
    );
    this.render();
  }

  render() {
    // Group nodes by category
    const categories = this.groupNodesByCategory(this.filteredNodes);

    this.element.innerHTML = '';

    if (this.filteredNodes.length === 0) {
      this.element.innerHTML = '<div class="no-results">No nodes found</div>';
      return;
    }

    // Render each category
    Object.entries(categories).forEach(([categoryName, nodes]) => {
      const categoryElement = this.createCategoryElement(categoryName, nodes);
      this.element.appendChild(categoryElement);
    });
  }

  groupNodesByCategory(nodes) {
    const categories = {};

    nodes.forEach(node => {
      const category = node.metadata.category || 'Other';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(node);
    });

    return categories;
  }

  createCategoryElement(categoryName, nodes) {
    const category = document.createElement('div');
    category.className = 'node-category';

    const header = document.createElement('h4');
    header.textContent = categoryName;
    category.appendChild(header);

    nodes.forEach(node => {
      const nodeItem = this.createNodeItem(node);
      category.appendChild(nodeItem);
    });

    return category;
  }

  createNodeItem(node) {
    const item = document.createElement('div');
    item.className = 'node-item';
    item.draggable = true;
    item.dataset.nodeType = node.id;

    // Icon
    const icon = document.createElement('span');
    icon.className = 'node-icon';
    if (node.visual.icon.type === 'unicode') {
      icon.textContent = node.visual.icon.value;
    } else if (node.visual.icon.type === 'fontawesome') {
      icon.className += ` ${node.visual.icon.value}`;
    }
    if (node.visual.icon.color) {
      icon.style.color = node.visual.icon.color;
    }

    // Name only (description will be shown in properties panel)
    const content = document.createElement('div');
    content.className = 'node-content';

    const name = document.createElement('div');
    name.className = 'node-name';
    name.textContent = node.name;

    content.appendChild(name);

    item.appendChild(icon);
    item.appendChild(content);

    // Setup drag and drop
    this.setupNodeItemDrag(item, node);

    return item;
  }

  setupNodeItemDrag(item, node) {
    item.addEventListener('dragstart', (e) => {
      e.dataTransfer.setData('text/node-type', node.id);
      e.dataTransfer.setData('application/json', JSON.stringify(node));
      item.classList.add('dragging');

      this.eventEmitter.emit('node:drag:start', {
        nodeType: node.id,
        nodeData: node
      });
    });

    item.addEventListener('dragend', () => {
      item.classList.remove('dragging');
      // Don't emit node:drag:end for palette items
      // This event is for actual node movement, not palette drag
    });

    // Click to add node at center
    item.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.eventEmitter.emit('node:add:center', {
        nodeType: node.id,
        nodeData: node
      });
    });
  }

  getNodeById(nodeId) {
    return this.availableNodes.find(node => node.id === nodeId);
  }

  refresh() {
    this.render();
  }
}
