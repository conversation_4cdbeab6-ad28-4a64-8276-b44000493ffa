<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Workflow System - Debug Mode</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/workflow-canvas.css">
    <link rel="stylesheet" href="css/node-styles.css">
    <link rel="stylesheet" href="css/properties-panel.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .debug-panel h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        
        .debug-step {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .debug-step.success {
            color: #4CAF50;
        }
        
        .debug-step.error {
            color: #F44336;
        }
        
        .debug-step.warning {
            color: #FF9800;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            font-size: 18px;
        }
        
        .loading-overlay.hidden {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div>
            <i class="fas fa-spinner fa-spin"></i>
            <span id="loading-text">Initializing Dynamic Workflow System...</span>
        </div>
    </div>

    <!-- Debug Panel -->
    <div id="debug-panel" class="debug-panel">
        <h4>🔍 Debug Console</h4>
        <div id="debug-log"></div>
    </div>

    <!-- Main Application Container -->
    <div id="app" class="app-container">

        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <h1 class="app-title">
                    <i class="fas fa-project-diagram"></i>
                    Dynamic Workflow System (Debug)
                </h1>
            </div>

            <div class="header-center">
                <div class="workflow-controls">
                    <button id="btn-new-workflow" class="btn btn-primary">
                        <i class="fas fa-plus"></i> New Workflow
                    </button>
                    <button id="btn-save-workflow" class="btn btn-success">
                        <i class="fas fa-save"></i> Save
                    </button>
                    <button id="btn-load-workflow" class="btn btn-info">
                        <i class="fas fa-folder-open"></i> Load
                    </button>
                    <button id="btn-execute-workflow" class="btn btn-warning">
                        <i class="fas fa-play"></i> Execute
                    </button>
                </div>
            </div>

            <div class="header-right">
                <div class="status-indicator">
                    <span id="connection-status" class="status-dot status-connecting"></span>
                    <span id="status-text">Connecting...</span>
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="app-main">

            <!-- Left Sidebar - Node Palette -->
            <aside class="sidebar sidebar-left">
                <div class="sidebar-header">
                    <h3>Node Palette</h3>
                    <div class="search-container">
                        <input type="text" id="node-search" placeholder="Search nodes..." class="search-input">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>

                <div class="sidebar-content">
                    <div id="node-categories" class="node-categories">
                        <!-- Node categories will be dynamically loaded here -->
                        <div class="category-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading nodes...
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Central Workflow Canvas -->
            <section class="workflow-canvas-container">
                <div class="canvas-toolbar">
                    <div class="toolbar-left">
                        <button id="btn-zoom-in" class="btn btn-sm">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button id="btn-zoom-out" class="btn btn-sm">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button id="btn-zoom-fit" class="btn btn-sm">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <span class="zoom-level">100%</span>
                    </div>

                    <div class="toolbar-center">
                        <span id="canvas-info" class="canvas-info">
                            Ready - Drop nodes here to build your workflow
                        </span>
                    </div>

                    <div class="toolbar-right">
                        <button id="btn-grid-toggle" class="btn btn-sm active">
                            <i class="fas fa-th"></i> Grid
                        </button>
                        <button id="btn-snap-toggle" class="btn btn-sm active">
                            <i class="fas fa-magnet"></i> Snap
                        </button>
                    </div>
                </div>

                <div id="workflow-canvas" class="workflow-canvas">
                    <svg id="canvas-svg" class="canvas-svg">
                        <!-- Grid pattern -->
                        <defs>
                            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#grid)" />

                        <!-- Connections layer -->
                        <g id="connections-layer" class="connections-layer"></g>

                        <!-- Nodes layer -->
                        <g id="nodes-layer" class="nodes-layer"></g>

                        <!-- Selection layer -->
                        <g id="selection-layer" class="selection-layer"></g>
                    </svg>

                    <!-- HTML overlay for node content -->
                    <div id="nodes-overlay" class="nodes-overlay"></div>
                </div>
            </section>

            <!-- Right Sidebar - Properties Panel -->
            <aside class="sidebar sidebar-right">
                <div class="sidebar-header">
                    <h3>Properties</h3>
                    <button id="btn-close-properties" class="btn btn-sm btn-ghost">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="sidebar-content">
                    <div id="properties-panel" class="properties-panel">
                        <div class="no-selection">
                            <i class="fas fa-mouse-pointer"></i>
                            <p>Select a node to view its properties</p>
                        </div>
                    </div>
                </div>
            </aside>

        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-left">
                <span id="node-count">0 nodes</span>
                <span class="separator">|</span>
                <span id="connection-count">0 connections</span>
            </div>

            <div class="footer-center">
                <div id="execution-status" class="execution-status">
                    <span class="status-text">Ready</span>
                </div>
            </div>

            <div class="footer-right">
                <span class="version">v1.0.0 (Debug)</span>
            </div>
        </footer>
    </div>

    <!-- Modal Dialogs -->
    <div id="modal-overlay" class="modal-overlay hidden">
        <div id="modal-container" class="modal-container">
            <!-- Modal content will be dynamically inserted here -->
        </div>
    </div>

    <!-- Context Menu -->
    <div id="context-menu" class="context-menu hidden">
        <!-- Context menu items will be dynamically inserted here -->
    </div>

    <!-- Debug JavaScript -->
    <script>
        // Debug logging
        const debugLog = document.getElementById('debug-log');
        const loadingOverlay = document.getElementById('loading-overlay');
        const loadingText = document.getElementById('loading-text');
        
        function addDebugLog(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `debug-step ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            debugLog.appendChild(div);
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }
        
        function updateLoadingText(text) {
            loadingText.textContent = text;
            addDebugLog(text);
        }
        
        // Override console methods for debugging
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            if (args[0] && typeof args[0] === 'string') {
                if (args[0].includes('✅')) {
                    addDebugLog(args[0], 'success');
                } else if (args[0].includes('❌')) {
                    addDebugLog(args[0], 'error');
                } else if (args[0].includes('🔄')) {
                    addDebugLog(args[0], 'warning');
                }
            }
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            if (args[0]) {
                addDebugLog('ERROR: ' + args[0], 'error');
            }
        };
        
        // Track initialization progress
        updateLoadingText('Loading scripts...');
        
        // Hide loading overlay when app is ready
        document.addEventListener('DOMContentLoaded', () => {
            updateLoadingText('DOM loaded, initializing components...');
        });
        
        // Global error handler
        window.addEventListener('error', (event) => {
            addDebugLog('SCRIPT ERROR: ' + event.message, 'error');
            console.error('Script error:', event.error);
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            addDebugLog('PROMISE REJECTION: ' + event.reason, 'error');
            console.error('Unhandled promise rejection:', event.reason);
        });
    </script>

    <!-- 安全的JavaScript实现 -->
    <script>
        // 简化的调试实现，避免加载有问题的原始脚本

        // 基础事件发射器
        class SimpleEventEmitter {
            constructor() {
                this.events = {};
            }

            on(event, callback) {
                if (!this.events[event]) {
                    this.events[event] = [];
                }
                this.events[event].push(callback);
            }

            emit(event, data) {
                if (this.events[event]) {
                    this.events[event].forEach(callback => {
                        try {
                            callback(data);
                        } catch (error) {
                            console.error('Event callback error:', error);
                        }
                    });
                }
            }
        }

        // 简化的应用实现
        class SafeDebugApp {
            constructor() {
                this.eventEmitter = new SimpleEventEmitter();
                this.state = { isInitialized: false };
                this.nodes = [];
            }

            async init() {
                try {
                    addDebugLog('开始安全初始化...', 'info');

                    // 模拟组件初始化
                    await this.initializeComponents();

                    // 加载节点数据
                    await this.loadNodes();

                    // 设置基础交互
                    this.setupBasicInteractions();

                    this.state.isInitialized = true;
                    addDebugLog('安全初始化完成', 'success');

                } catch (error) {
                    addDebugLog('初始化失败: ' + error.message, 'error');
                }
            }

            async initializeComponents() {
                addDebugLog('初始化组件...', 'info');

                // 检查必要的DOM元素
                const requiredElements = [
                    'workflow-canvas',
                    'node-categories',
                    'properties-panel',
                    'nodes-overlay'
                ];

                for (const id of requiredElements) {
                    const element = document.getElementById(id);
                    if (element) {
                        addDebugLog(`✓ 找到元素: ${id}`, 'success');
                    } else {
                        addDebugLog(`✗ 缺失元素: ${id}`, 'error');
                    }
                }
            }

            async loadNodes() {
                try {
                    addDebugLog('加载节点数据...', 'info');
                    const response = await fetch('/api/nodes');

                    if (response.ok) {
                        this.nodes = await response.json();
                        addDebugLog(`✓ 加载了 ${this.nodes.length} 个节点类型`, 'success');
                        this.renderNodePalette();
                    } else {
                        throw new Error(`API响应错误: ${response.status}`);
                    }
                } catch (error) {
                    addDebugLog('节点加载失败: ' + error.message, 'error');
                    this.loadDefaultNodes();
                }
            }

            loadDefaultNodes() {
                this.nodes = [
                    { id: 'task', name: '任务节点', visual: { icon: { value: '🔧' } } },
                    { id: 'condition', name: '条件节点', visual: { icon: { value: '🔀' } } },
                    { id: 'loop', name: '循环节点', visual: { icon: { value: '🔄' } } }
                ];
                addDebugLog('使用默认节点', 'warning');
                this.renderNodePalette();
            }

            renderNodePalette() {
                const container = document.getElementById('node-categories');
                if (!container) return;

                container.innerHTML = '<h4>节点类型</h4>';

                this.nodes.forEach(node => {
                    const item = document.createElement('div');
                    item.className = 'node-item';
                    item.innerHTML = `
                        <span>${node.visual?.icon?.value || '📦'}</span>
                        <span>${node.name}</span>
                    `;
                    item.style.cssText = `
                        padding: 10px;
                        margin: 5px 0;
                        background: #e3f2fd;
                        border: 1px solid #2196f3;
                        border-radius: 4px;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    `;

                    item.addEventListener('click', () => {
                        addDebugLog(`点击节点: ${node.name}`, 'info');
                    });

                    container.appendChild(item);
                });

                addDebugLog('节点调色板已渲染', 'success');
            }

            setupBasicInteractions() {
                addDebugLog('设置基础交互...', 'info');

                // 简单的画布交互
                const canvas = document.getElementById('workflow-canvas');
                if (canvas) {
                    canvas.addEventListener('click', () => {
                        addDebugLog('画布点击', 'info');
                    });
                }

                addDebugLog('基础交互已设置', 'success');
            }
        }

        // 初始化应用
        window.addEventListener('load', () => {
            updateLoadingText('启动安全调试模式...');

            window.app = new SafeDebugApp();

            // 延迟初始化以避免阻塞
            setTimeout(async () => {
                await window.app.init();

                setTimeout(() => {
                    const loadingOverlay = document.getElementById('loading-overlay');
                    if (loadingOverlay) {
                        loadingOverlay.classList.add('hidden');
                    }
                    addDebugLog('调试模式就绪', 'success');
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
