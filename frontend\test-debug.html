<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Dynamic Workflow System</title>
    
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .canvas-area {
            flex: 1;
            position: relative;
            background: white;
            border: 2px solid #ccc;
        }
        
        .properties-area {
            width: 300px;
            background: #f9f9f9;
            border-left: 2px solid #ccc;
            padding: 20px;
        }
        
        .workflow-canvas {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #fafafa;
        }
        
        .nodes-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .workflow-node {
            position: absolute;
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            cursor: move;
            user-select: none;
            min-width: 120px;
            min-height: 60px;
        }
        
        .workflow-node.selected {
            border-color: #2196F3;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
        }
        
        .workflow-node.dragging {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            transform: scale(1.05);
            opacity: 0.9;
            z-index: 1000;
        }
        
        .node-header {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .test-controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .test-controls button {
            margin: 5px;
            padding: 5px 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <button onclick="createTestNode()">Create Node</button>
        <button onclick="testNodeSelection()">Test Selection</button>
        <button onclick="checkNodePositions()">Check Positions</button>
        <button onclick="clearNodes()">Clear All</button>
    </div>

    <div class="container">
        <div class="canvas-area">
            <div id="workflow-canvas" class="workflow-canvas">
                <div id="nodes-overlay" class="nodes-overlay"></div>
            </div>
        </div>
        
        <div class="properties-area">
            <h3>Properties Panel</h3>
            <div id="properties-content">
                <p>No node selected</p>
            </div>
        </div>
    </div>

    <script>
        let nodeCounter = 0;
        let selectedNode = null;
        
        function createTestNode() {
            nodeCounter++;
            const node = document.createElement('div');
            node.className = 'workflow-node';
            node.id = `test-node-${nodeCounter}`;
            node.innerHTML = `
                <div class="node-header">Test Node ${nodeCounter}</div>
                <div>Click to select</div>
            `;
            
            // Random position
            node.style.left = Math.random() * 400 + 'px';
            node.style.top = Math.random() * 300 + 'px';
            
            // Add event listeners
            setupNodeEvents(node);
            
            document.getElementById('nodes-overlay').appendChild(node);
            console.log('Created node:', node.id, 'at position:', node.style.left, node.style.top);
        }
        
        function setupNodeEvents(node) {
            let isDragging = false;
            let dragOffset = { x: 0, y: 0 };
            
            // Click to select
            node.addEventListener('click', (e) => {
                e.stopPropagation();
                selectNode(node);
            });
            
            // Drag functionality
            node.addEventListener('mousedown', (e) => {
                isDragging = true;
                const rect = node.getBoundingClientRect();
                const containerRect = document.getElementById('nodes-overlay').getBoundingClientRect();
                
                dragOffset.x = e.clientX - rect.left;
                dragOffset.y = e.clientY - rect.top;
                
                node.classList.add('dragging');
                console.log('Started dragging:', node.id);
                
                const mouseMoveHandler = (e) => {
                    if (!isDragging) return;
                    
                    const containerRect = document.getElementById('nodes-overlay').getBoundingClientRect();
                    const x = e.clientX - containerRect.left - dragOffset.x;
                    const y = e.clientY - containerRect.top - dragOffset.y;
                    
                    node.style.left = Math.max(0, x) + 'px';
                    node.style.top = Math.max(0, y) + 'px';
                    
                    console.log('Dragging:', node.id, 'to', x, y);
                };
                
                const mouseUpHandler = () => {
                    if (!isDragging) return;
                    
                    isDragging = false;
                    node.classList.remove('dragging');
                    console.log('Stopped dragging:', node.id);
                    
                    document.removeEventListener('mousemove', mouseMoveHandler);
                    document.removeEventListener('mouseup', mouseUpHandler);
                };
                
                document.addEventListener('mousemove', mouseMoveHandler);
                document.addEventListener('mouseup', mouseUpHandler);
            });
        }
        
        function selectNode(node) {
            // Clear previous selection
            if (selectedNode) {
                selectedNode.classList.remove('selected');
            }
            
            // Select new node
            selectedNode = node;
            node.classList.add('selected');
            
            // Update properties panel
            document.getElementById('properties-content').innerHTML = `
                <h4>${node.id}</h4>
                <p>Position: ${node.style.left}, ${node.style.top}</p>
                <p>Selected at: ${new Date().toLocaleTimeString()}</p>
            `;
            
            console.log('Selected node:', node.id);
        }
        
        function testNodeSelection() {
            const nodes = document.querySelectorAll('.workflow-node');
            if (nodes.length > 0) {
                selectNode(nodes[0]);
            } else {
                alert('No nodes to select. Create a node first.');
            }
        }
        
        function checkNodePositions() {
            const nodes = document.querySelectorAll('.workflow-node');
            const overlay = document.getElementById('nodes-overlay');
            
            console.log('=== NODE POSITION CHECK ===');
            console.log('Overlay transform:', window.getComputedStyle(overlay).transform);
            
            nodes.forEach((node, index) => {
                const rect = node.getBoundingClientRect();
                const style = window.getComputedStyle(node);
                
                console.log(`Node ${index + 1} (${node.id}):`, {
                    styleLeft: style.left,
                    styleTop: style.top,
                    rectX: rect.x,
                    rectY: rect.y,
                    transform: style.transform
                });
            });
        }
        
        function clearNodes() {
            document.getElementById('nodes-overlay').innerHTML = '';
            document.getElementById('properties-content').innerHTML = '<p>No node selected</p>';
            selectedNode = null;
            nodeCounter = 0;
            console.log('Cleared all nodes');
        }
        
        // Canvas click to deselect
        document.getElementById('workflow-canvas').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                if (selectedNode) {
                    selectedNode.classList.remove('selected');
                    selectedNode = null;
                    document.getElementById('properties-content').innerHTML = '<p>No node selected</p>';
                    console.log('Deselected all nodes');
                }
            }
        });
        
        console.log('Debug test page loaded');
    </script>
</body>
</html>
