<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Test - 紧急诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .test-section {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
        #console {
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .timestamp {
            color: #888;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 紧急诊断工具</h1>
        <p>这个页面用于诊断Dynamic Workflow System的问题。如果这个页面也卡死，说明问题可能在浏览器或系统层面。</p>
        
        <div class="status info">
            <strong>当前状态:</strong> <span id="current-status">正在初始化...</span>
        </div>
        
        <div class="test-grid">
            <div class="test-section">
                <h3>🔧 基础功能测试</h3>
                <button onclick="testBasics()">运行基础测试</button>
                <div id="basic-results"></div>
            </div>
            
            <div class="test-section">
                <h3>🌐 网络连接测试</h3>
                <button onclick="testNetwork()">测试网络</button>
                <div id="network-results"></div>
            </div>
            
            <div class="test-section">
                <h3>📁 文件加载测试</h3>
                <button onclick="testFiles()">测试文件</button>
                <div id="file-results"></div>
            </div>
            
            <div class="test-section">
                <h3>🎨 简单UI测试</h3>
                <button onclick="testUI()">测试UI</button>
                <div id="ui-results"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 实时控制台</h3>
            <button onclick="clearConsole()">清除</button>
            <button onclick="exportLog()">导出日志</button>
            <div id="console"></div>
        </div>
        
        <div class="test-section">
            <h3>🔄 快速修复</h3>
            <button onclick="clearCache()">清除缓存</button>
            <button onclick="resetSettings()">重置设置</button>
            <button onclick="forceReload()">强制刷新</button>
        </div>
    </div>

    <script>
        // 全局状态
        let testResults = {};
        let logEntries = [];
        
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = { timestamp, message, type };
            logEntries.push(entry);

            const consoleDiv = document.getElementById('console');
            const div = document.createElement('div');

            const color = {
                'error': '#ff4444',
                'success': '#44ff44',
                'warning': '#ffaa44',
                'info': '#44aaff'
            }[type] || '#00ff00';

            div.innerHTML = `<span class="timestamp">[${timestamp}]</span> <span style="color: ${color}">${message}</span>`;
            consoleDiv.appendChild(div);
            consoleDiv.scrollTop = consoleDiv.scrollHeight;

            // 也输出到浏览器控制台
            window.console[type === 'error' ? 'error' : 'log'](`[${timestamp}] ${message}`);
        }
        
        function updateStatus(status) {
            document.getElementById('current-status').textContent = status;
            log(`状态更新: ${status}`, 'info');
        }
        
        function addResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        // 基础功能测试
        function testBasics() {
            log('开始基础功能测试', 'info');
            const container = document.getElementById('basic-results');
            container.innerHTML = '';
            
            try {
                // 测试JavaScript基础
                const arr = [1, 2, 3];
                const obj = { test: true };
                const promise = Promise.resolve('test');
                addResult('basic-results', '✅ JavaScript基础功能正常', 'success');
                log('JavaScript基础功能测试通过', 'success');
                
                // 测试DOM操作
                const testDiv = document.createElement('div');
                testDiv.textContent = 'test';
                addResult('basic-results', '✅ DOM操作正常', 'success');
                log('DOM操作测试通过', 'success');
                
                // 测试事件
                const testEvent = new CustomEvent('test');
                document.dispatchEvent(testEvent);
                addResult('basic-results', '✅ 事件系统正常', 'success');
                log('事件系统测试通过', 'success');
                
                // 测试存储
                localStorage.setItem('test', 'value');
                localStorage.removeItem('test');
                addResult('basic-results', '✅ 本地存储正常', 'success');
                log('本地存储测试通过', 'success');
                
                testResults.basics = true;
                updateStatus('基础功能测试完成');
                
            } catch (error) {
                addResult('basic-results', '❌ 基础功能异常: ' + error.message, 'error');
                log('基础功能测试失败: ' + error.message, 'error');
                testResults.basics = false;
            }
        }
        
        // 网络连接测试
        async function testNetwork() {
            log('开始网络连接测试', 'info');
            const container = document.getElementById('network-results');
            container.innerHTML = '';
            
            try {
                // 测试健康检查
                log('测试 /api/health 端点...', 'info');
                const healthResponse = await fetch('/api/health');
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    addResult('network-results', '✅ 服务器健康检查正常', 'success');
                    log('服务器健康检查通过: ' + JSON.stringify(healthData), 'success');
                } else {
                    throw new Error(`健康检查失败: ${healthResponse.status}`);
                }
                
                // 测试节点API
                log('测试 /api/nodes 端点...', 'info');
                const nodesResponse = await fetch('/api/nodes');
                if (nodesResponse.ok) {
                    const nodesData = await nodesResponse.json();
                    addResult('network-results', `✅ 节点API正常，获取到 ${nodesData.length} 个节点`, 'success');
                    log(`节点API测试通过，节点数量: ${nodesData.length}`, 'success');
                    
                    // 显示节点详情
                    nodesData.forEach((node, index) => {
                        log(`节点 ${index + 1}: ${node.name} (${node.id})`, 'info');
                    });
                } else {
                    throw new Error(`节点API失败: ${nodesResponse.status}`);
                }
                
                testResults.network = true;
                updateStatus('网络连接测试完成');
                
            } catch (error) {
                addResult('network-results', '❌ 网络连接失败: ' + error.message, 'error');
                log('网络连接测试失败: ' + error.message, 'error');
                testResults.network = false;
            }
        }
        
        // 文件加载测试
        function testFiles() {
            log('开始文件加载测试', 'info');
            const container = document.getElementById('file-results');
            container.innerHTML = '';
            
            const criticalFiles = [
                'css/main.css',
                'js/utils/eventEmitter.js',
                'js/app.js'
            ];
            
            let loadedCount = 0;
            let totalCount = criticalFiles.length;
            
            criticalFiles.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            loadedCount++;
                            addResult('file-results', `✅ ${file} 可访问`, 'success');
                            log(`文件可访问: ${file}`, 'success');
                        } else {
                            addResult('file-results', `❌ ${file} 访问失败 (${response.status})`, 'error');
                            log(`文件访问失败: ${file} (${response.status})`, 'error');
                        }
                        
                        if (loadedCount + (totalCount - loadedCount) === totalCount) {
                            testResults.files = loadedCount === totalCount;
                            updateStatus(`文件测试完成: ${loadedCount}/${totalCount} 成功`);
                        }
                    })
                    .catch(error => {
                        addResult('file-results', `❌ ${file} 加载错误: ${error.message}`, 'error');
                        log(`文件加载错误: ${file} - ${error.message}`, 'error');
                    });
            });
        }
        
        // UI测试
        function testUI() {
            log('开始UI测试', 'info');
            const container = document.getElementById('ui-results');
            container.innerHTML = '';
            
            try {
                // 创建测试元素
                const testDiv = document.createElement('div');
                testDiv.style.cssText = 'width: 100px; height: 50px; background: #007bff; margin: 10px 0; border-radius: 4px;';
                testDiv.textContent = '测试元素';
                container.appendChild(testDiv);
                
                // 测试动画
                testDiv.style.transition = 'transform 0.3s ease';
                setTimeout(() => {
                    testDiv.style.transform = 'translateX(20px)';
                }, 100);
                
                addResult('ui-results', '✅ UI元素创建和动画正常', 'success');
                log('UI测试通过', 'success');
                
                testResults.ui = true;
                updateStatus('UI测试完成');
                
            } catch (error) {
                addResult('ui-results', '❌ UI测试失败: ' + error.message, 'error');
                log('UI测试失败: ' + error.message, 'error');
                testResults.ui = false;
            }
        }
        
        // 工具函数
        function clearConsole() {
            document.getElementById('console').innerHTML = '';
            logEntries = [];
            log('控制台已清除', 'info');
        }
        
        function exportLog() {
            const logText = logEntries.map(entry => 
                `[${entry.timestamp}] ${entry.type.toUpperCase()}: ${entry.message}`
            ).join('\n');
            
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `workflow-debug-${new Date().toISOString().slice(0, 19)}.log`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('日志已导出', 'success');
        }
        
        function clearCache() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                log('缓存已清除', 'success');
                updateStatus('缓存清除完成');
            } catch (error) {
                log('清除缓存失败: ' + error.message, 'error');
            }
        }
        
        function resetSettings() {
            try {
                // 清除所有工作流相关的存储
                Object.keys(localStorage).forEach(key => {
                    if (key.startsWith('workflow_') || key.startsWith('dynamic_')) {
                        localStorage.removeItem(key);
                    }
                });
                log('设置已重置', 'success');
                updateStatus('设置重置完成');
            } catch (error) {
                log('重置设置失败: ' + error.message, 'error');
            }
        }
        
        function forceReload() {
            log('执行强制刷新...', 'warning');
            setTimeout(() => {
                location.reload(true);
            }, 1000);
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('紧急诊断工具已加载', 'success');
            updateStatus('就绪 - 请运行测试');
        });
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.message} 在 ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
        
        // 初始化完成
        log('紧急诊断工具初始化完成', 'success');
    </script>
</body>
</html>
