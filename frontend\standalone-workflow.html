<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>独立工作流编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            overflow: hidden;
        }
        
        .app {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #fafafa;
        }
        
        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .main-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .toolbar {
            height: 50px;
            background: white;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            padding: 0 20px;
            gap: 10px;
        }
        
        .canvas-container {
            flex: 1;
            position: relative;
            background: #fafafa;
            overflow: hidden;
        }
        
        .canvas {
            width: 100%;
            height: 100%;
            position: relative;
            cursor: grab;
            transform-origin: 0 0;
        }
        
        .canvas:active {
            cursor: grabbing;
        }
        
        .node-item {
            padding: 12px;
            margin: 8px 0;
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            cursor: move;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.2s ease;
        }
        
        .node-item:hover {
            background: #bbdefb;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .node-item.dragging {
            opacity: 0.5;
        }
        
        .workflow-node {
            position: absolute;
            min-width: 120px;
            min-height: 60px;
            background: white;
            border: 2px solid #2196f3;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: move;
            user-select: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
            font-weight: 500;
            padding: 10px;
            text-align: center;
        }
        
        .workflow-node:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }
        
        .workflow-node.dragging {
            opacity: 0.8;
            transform: rotate(2deg) scale(1.02);
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .workflow-node.selected {
            border-color: #ff9800;
            box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.3);
        }
        
        button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        button:hover {
            background: #f0f0f0;
            border-color: #2196f3;
        }
        
        .status {
            margin-left: auto;
            padding: 4px 8px;
            background: #4caf50;
            color: white;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image: 
                linear-gradient(to right, #000 1px, transparent 1px),
                linear-gradient(to bottom, #000 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
        }
        
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="app">
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>🎨 节点调色板</h3>
            </div>
            <div class="sidebar-content">
                <div class="node-item" draggable="true" data-type="task">
                    <span>🔧</span>
                    <span>任务节点</span>
                </div>
                <div class="node-item" draggable="true" data-type="condition">
                    <span>🔀</span>
                    <span>条件节点</span>
                </div>
                <div class="node-item" draggable="true" data-type="loop">
                    <span>🔄</span>
                    <span>循环节点</span>
                </div>
                <div class="node-item" draggable="true" data-type="input">
                    <span>📥</span>
                    <span>输入节点</span>
                </div>
                <div class="node-item" draggable="true" data-type="output">
                    <span>📤</span>
                    <span>输出节点</span>
                </div>
                <div class="node-item" draggable="true" data-type="transform">
                    <span>⚙️</span>
                    <span>转换节点</span>
                </div>
            </div>
        </div>
        
        <div class="main-area">
            <div class="toolbar">
                <button onclick="zoomIn()">🔍+ 放大</button>
                <button onclick="zoomOut()">🔍- 缩小</button>
                <button onclick="resetView()">🎯 重置</button>
                <button onclick="clearCanvas()">🗑️ 清空</button>
                <button onclick="toggleGrid()">📐 网格</button>
                <span id="zoom-display">100%</span>
                <div class="status">就绪</div>
            </div>
            
            <div class="canvas-container">
                <div class="grid" id="grid"></div>
                <div class="canvas" id="canvas">
                    <!-- 节点将在这里创建 -->
                </div>
                
                <div class="info-panel">
                    <div>节点数量: <span id="node-count">0</span></div>
                    <div>缩放: <span id="zoom-info">100%</span></div>
                    <div>位置: <span id="pan-info">0, 0</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态
        let zoom = 1;
        let pan = { x: 0, y: 0 };
        let nodeCounter = 0;
        let selectedNode = null;
        let isDraggingCanvas = false;
        let isDraggingNode = false;
        let dragStart = { x: 0, y: 0 };
        let showGrid = true;
        
        const canvas = document.getElementById('canvas');
        const grid = document.getElementById('grid');
        
        // 节点类型配置
        const nodeTypes = {
            task: { icon: '🔧', name: '任务', color: '#2196f3' },
            condition: { icon: '🔀', name: '条件', color: '#4caf50' },
            loop: { icon: '🔄', name: '循环', color: '#ff9800' },
            input: { icon: '📥', name: '输入', color: '#9c27b0' },
            output: { icon: '📤', name: '输出', color: '#f44336' },
            transform: { icon: '⚙️', name: '转换', color: '#607d8b' }
        };
        
        // 初始化
        function init() {
            setupEventListeners();
            updateDisplay();
            console.log('独立工作流编辑器已初始化');
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 节点调色板拖拽
            document.querySelectorAll('.node-item').forEach(item => {
                item.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/node-type', item.dataset.type);
                    item.classList.add('dragging');
                });
                
                item.addEventListener('dragend', () => {
                    item.classList.remove('dragging');
                });
            });
            
            // 画布拖拽和放置
            canvas.addEventListener('dragover', (e) => {
                e.preventDefault();
            });
            
            canvas.addEventListener('drop', (e) => {
                e.preventDefault();
                const nodeType = e.dataTransfer.getData('text/node-type');
                if (nodeType) {
                    const rect = canvas.getBoundingClientRect();
                    const x = (e.clientX - rect.left - pan.x) / zoom;
                    const y = (e.clientY - rect.top - pan.y) / zoom;
                    createNode(nodeType, x, y);
                }
            });
            
            // 画布平移
            canvas.addEventListener('mousedown', (e) => {
                if (e.target === canvas || e.target === grid) {
                    isDraggingCanvas = true;
                    dragStart = { x: e.clientX - pan.x, y: e.clientY - pan.y };
                    canvas.style.cursor = 'grabbing';
                }
            });
            
            document.addEventListener('mousemove', (e) => {
                if (isDraggingCanvas) {
                    pan.x = e.clientX - dragStart.x;
                    pan.y = e.clientY - dragStart.y;
                    updateTransform();
                    updateDisplay();
                }
            });
            
            document.addEventListener('mouseup', () => {
                isDraggingCanvas = false;
                canvas.style.cursor = 'grab';
            });
            
            // 缩放
            canvas.addEventListener('wheel', (e) => {
                e.preventDefault();
                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                const rect = canvas.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                
                const oldZoom = zoom;
                zoom = Math.max(0.2, Math.min(3, zoom * delta));
                
                // 向鼠标位置缩放
                pan.x = mouseX - (mouseX - pan.x) * (zoom / oldZoom);
                pan.y = mouseY - (mouseY - pan.y) * (zoom / oldZoom);
                
                updateTransform();
                updateDisplay();
            });
            
            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Delete' && selectedNode) {
                    deleteNode(selectedNode);
                }
                if (e.key === 'Escape') {
                    clearSelection();
                }
            });
        }
        
        // 创建节点
        function createNode(type, x, y) {
            nodeCounter++;
            const config = nodeTypes[type];
            
            const node = document.createElement('div');
            node.className = 'workflow-node';
            node.dataset.nodeId = `${type}_${nodeCounter}`;
            node.dataset.nodeType = type;
            node.style.left = x + 'px';
            node.style.top = y + 'px';
            node.style.borderColor = config.color;
            
            node.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; gap: 5px;">
                    <div style="font-size: 20px;">${config.icon}</div>
                    <div style="font-size: 12px;">${config.name} ${nodeCounter}</div>
                </div>
            `;
            
            // 节点事件
            node.addEventListener('mousedown', (e) => {
                e.stopPropagation();
                selectNode(node);
                startNodeDrag(node, e);
            });
            
            canvas.appendChild(node);
            updateDisplay();
            
            console.log(`创建节点: ${config.name} ${nodeCounter} 在 (${Math.round(x)}, ${Math.round(y)})`);
        }
        
        // 节点拖拽
        function startNodeDrag(node, e) {
            isDraggingNode = true;
            const rect = node.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            
            const offsetX = e.clientX - rect.left;
            const offsetY = e.clientY - rect.top;
            
            node.classList.add('dragging');
            
            const mouseMoveHandler = (e) => {
                if (!isDraggingNode) return;
                
                const x = (e.clientX - canvasRect.left - offsetX - pan.x) / zoom;
                const y = (e.clientY - canvasRect.top - offsetY - pan.y) / zoom;
                
                node.style.left = Math.max(0, x) + 'px';
                node.style.top = Math.max(0, y) + 'px';
            };
            
            const mouseUpHandler = () => {
                isDraggingNode = false;
                node.classList.remove('dragging');
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            };
            
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);
        }
        
        // 节点选择
        function selectNode(node) {
            clearSelection();
            selectedNode = node;
            node.classList.add('selected');
        }
        
        function clearSelection() {
            if (selectedNode) {
                selectedNode.classList.remove('selected');
                selectedNode = null;
            }
        }
        
        // 删除节点
        function deleteNode(node) {
            node.remove();
            clearSelection();
            updateDisplay();
            console.log('删除节点: ' + node.dataset.nodeId);
        }
        
        // 变换更新
        function updateTransform() {
            canvas.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;
            grid.style.transform = `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`;
        }
        
        // 显示更新
        function updateDisplay() {
            const nodeCount = canvas.querySelectorAll('.workflow-node').length;
            document.getElementById('node-count').textContent = nodeCount;
            document.getElementById('zoom-display').textContent = Math.round(zoom * 100) + '%';
            document.getElementById('zoom-info').textContent = Math.round(zoom * 100) + '%';
            document.getElementById('pan-info').textContent = `${Math.round(pan.x)}, ${Math.round(pan.y)}`;
        }
        
        // 工具栏功能
        function zoomIn() {
            zoom = Math.min(zoom * 1.2, 3);
            updateTransform();
            updateDisplay();
        }
        
        function zoomOut() {
            zoom = Math.max(zoom * 0.8, 0.2);
            updateTransform();
            updateDisplay();
        }
        
        function resetView() {
            zoom = 1;
            pan = { x: 0, y: 0 };
            updateTransform();
            updateDisplay();
        }
        
        function clearCanvas() {
            if (confirm('确定要清空所有节点吗？')) {
                canvas.querySelectorAll('.workflow-node').forEach(node => node.remove());
                clearSelection();
                nodeCounter = 0;
                updateDisplay();
                console.log('画布已清空');
            }
        }
        
        function toggleGrid() {
            showGrid = !showGrid;
            grid.style.display = showGrid ? 'block' : 'none';
            console.log('网格显示: ' + (showGrid ? '开启' : '关闭'));
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
        
        console.log('独立工作流编辑器脚本已加载');
    </script>
</body>
</html>
