/**
 * API Client for Dynamic Workflow System
 * Handles all HTTP communication with the backend
 */

class ApiClient {
  constructor() {
    this.baseURL = '';
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    };
    this.timeout = 30000; // 30 seconds
  }

  setBaseURL(url) {
    this.baseURL = url.replace(/\/$/, ''); // Remove trailing slash
  }

  setDefaultHeader(key, value) {
    this.defaultHeaders[key] = value;
  }

  setTimeout(timeout) {
    this.timeout = timeout;
  }

  async request(method, endpoint, data = null, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    const config = {
      method: method.toUpperCase(),
      headers: {
        ...this.defaultHeaders,
        ...options.headers
      },
      ...options
    };

    // Add body for POST, PUT, PATCH requests
    if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
      config.body = JSON.stringify(data);
    }

    // Add timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    config.signal = controller.signal;

    try {
      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      // Handle different response types
      const contentType = response.headers.get('content-type');
      let responseData;

      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      if (!response.ok) {
        throw new ApiError(
          response.status,
          response.statusText,
          responseData,
          url
        );
      }

      return responseData;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        throw new ApiError(408, 'Request Timeout', 'Request timed out', url);
      }

      if (error instanceof ApiError) {
        throw error;
      }

      // Network or other errors
      throw new ApiError(0, 'Network Error', error.message, url);
    }
  }

  // Convenience methods
  async get(endpoint, options = {}) {
    return this.request('GET', endpoint, null, options);
  }

  async post(endpoint, data, options = {}) {
    return this.request('POST', endpoint, data, options);
  }

  async put(endpoint, data, options = {}) {
    return this.request('PUT', endpoint, data, options);
  }

  async patch(endpoint, data, options = {}) {
    return this.request('PATCH', endpoint, data, options);
  }

  async delete(endpoint, options = {}) {
    return this.request('DELETE', endpoint, null, options);
  }

  // Workflow-specific API methods
  async getNodes() {
    return this.get('/api/nodes');
  }

  async getNode(nodeId) {
    return this.get(`/api/nodes/${nodeId}`);
  }

  async executeWorkflow(workflowData, inputs = {}) {
    return this.post('/api/workflow/execute', {
      workflow: workflowData,
      inputs: inputs
    });
  }

  async executeNode(nodeId, inputs, config = {}) {
    const node = await this.getNode(nodeId);
    return this.post(node.backend.endpoint, {
      inputs: inputs,
      config: config
    });
  }

  async getHealth() {
    return this.get('/api/health');
  }

  // File upload helper
  async uploadFile(endpoint, file, additionalData = {}) {
    const formData = new FormData();
    formData.append('file', file);

    // Add additional data
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key]);
    });

    return this.request('POST', endpoint, null, {
      body: formData,
      headers: {} // Let browser set Content-Type for FormData
    });
  }

  // Batch requests
  async batch(requests) {
    const promises = requests.map(req =>
      this.request(req.method, req.endpoint, req.data, req.options)
        .catch(error => ({ error, request: req }))
    );

    return Promise.all(promises);
  }
}

/**
 * Custom API Error class
 */
class ApiError extends Error {
  constructor(status, statusText, data, url) {
    super(`API Error ${status}: ${statusText}`);
    this.name = 'ApiError';
    this.status = status;
    this.statusText = statusText;
    this.data = data;
    this.url = url;
  }

  toString() {
    return `${this.name}: ${this.status} ${this.statusText} - ${this.url}`;
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ApiClient, ApiError };
}