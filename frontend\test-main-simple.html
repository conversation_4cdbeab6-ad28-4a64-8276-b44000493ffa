<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Main App - Dynamic Workflow</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/workflow-canvas.css">
    <link rel="stylesheet" href="css/node-styles.css">
    <link rel="stylesheet" href="css/properties-panel.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <h1>🔧 Test Dynamic Workflow System</h1>
            </div>
            <div class="header-center">
                <button class="btn btn-primary">+ New Workflow</button>
                <button class="btn btn-secondary">Save</button>
                <button class="btn btn-secondary">Load</button>
                <button class="btn btn-success">Execute</button>
            </div>
            <div class="header-right">
                <div class="connection-status connected">Connected</div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Left Sidebar - Node Palette -->
            <aside class="sidebar sidebar-left">
                <div class="sidebar-header">
                    <h3>Node Palette</h3>
                    <div class="search-container">
                        <input type="text" id="node-search" placeholder="Search nodes..." class="search-input">
                    </div>
                </div>
                <div class="sidebar-content">
                    <div id="node-categories">
                        <!-- Test nodes -->
                        <div class="node-category">
                            <h4>Control Flow</h4>
                            <div class="node-item" draggable="true" data-node-type="conditional">
                                <span class="node-icon">🔀</span>
                                <div class="node-content">
                                    <div class="node-name">Conditional Decision</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Center - Canvas Area -->
            <div class="canvas-container">
                <!-- Canvas Controls -->
                <div class="canvas-controls">
                    <div class="zoom-controls">
                        <button class="zoom-btn" id="zoom-out">-</button>
                        <button class="zoom-btn" id="zoom-in">+</button>
                        <button class="zoom-btn" id="zoom-reset">⌂</button>
                        <span class="zoom-level">100%</span>
                    </div>
                    <div class="canvas-status">Ready - Drop nodes here to build your workflow</div>
                    <div class="canvas-options">
                        <button class="option-btn" id="toggle-grid">Grid</button>
                        <button class="option-btn" id="toggle-snap">Snap</button>
                    </div>
                </div>

                <!-- Main Canvas -->
                <div class="workflow-canvas" id="workflow-canvas">
                    <svg class="canvas-svg" id="canvas-svg">
                        <defs>
                            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#333" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#grid)" />
                        <g id="connections-layer"></g>
                    </svg>
                    <div class="nodes-overlay" id="nodes-overlay"></div>
                </div>
            </div>

            <!-- Right Sidebar - Properties Panel -->
            <aside class="sidebar sidebar-right">
                <div class="sidebar-header">
                    <h3>Properties</h3>
                    <button class="btn btn-icon" id="close-properties">×</button>
                </div>
                <div class="sidebar-content">
                    <div id="properties-panel">
                        <div class="no-selection">
                            <p>Select a node to view its properties</p>
                        </div>
                    </div>
                </div>
            </aside>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-left">
                <span id="node-count">0 nodes</span>
                <span class="separator">|</span>
                <span id="connection-count">0 connections</span>
            </div>
            <div class="footer-center">
                <span id="app-status">Ready</span>
            </div>
            <div class="footer-right">
                <span>v1.0.0</span>
            </div>
        </footer>
    </div>

    <!-- Core JavaScript Files -->
    <script src="js/utils/eventEmitter.js"></script>
    <script src="js/utils/apiClient.js"></script>
    <script src="js/utils/storage.js"></script>

    <!-- Core Components -->
    <script src="js/core/workflowEngine.js"></script>
    <script src="js/core/nodeManager.js"></script>
    <script src="js/core/connectionManager.js"></script>

    <!-- UI Components -->
    <script src="js/components/workflowCanvas.js"></script>
    <script src="js/components/nodePalette.js"></script>
    <script src="js/components/propertiesPanel.js"></script>
    <script src="js/components/nodeRenderer.js?v=1751893000"></script>

    <!-- Test Script -->
    <script>
        console.log('🔧 Test Main App Loading...');
        
        // Simple test initialization
        try {
            const eventEmitter = new EventEmitter();
            const canvas = new WorkflowCanvas(document.getElementById('workflow-canvas'), eventEmitter);
            const nodeRenderer = new NodeRenderer(eventEmitter);
            
            // Set up node renderer
            const nodesOverlay = document.getElementById('nodes-overlay');
            if (nodesOverlay) {
                nodeRenderer.setContainer(nodesOverlay);
                nodeRenderer.setCanvas(canvas);
                console.log('✅ NodeRenderer setup complete');
            }
            
            // Test zoom controls
            document.getElementById('zoom-in').addEventListener('click', () => {
                canvas.zoomIn();
                console.log('Zoom in clicked');
            });
            
            document.getElementById('zoom-out').addEventListener('click', () => {
                canvas.zoomOut();
                console.log('Zoom out clicked');
            });
            
            document.getElementById('zoom-reset').addEventListener('click', () => {
                canvas.resetZoom();
                console.log('Zoom reset clicked');
            });
            
            // Test node creation
            document.querySelector('.node-item').addEventListener('click', () => {
                const nodeData = {
                    id: 'test_node_' + Date.now(),
                    type: 'conditional',
                    name: 'Test Conditional',
                    description: 'Test node for debugging',
                    position: { x: 200, y: 200 },
                    visual: {
                        icon: { type: 'unicode', value: '🔀' },
                        shape: 'rectangle',
                        sizing: { width: 120, height: 80 }
                    },
                    pins: {
                        inputs: [
                            { name: 'input', label: 'Input Data', dataType: 'any' }
                        ],
                        outputs: [
                            { name: 'true', label: 'True Branch', dataType: 'any' },
                            { name: 'false', label: 'False Branch', dataType: 'any' }
                        ]
                    }
                };
                
                const nodeElement = nodeRenderer.renderNode(nodeData);
                console.log('✅ Test node created:', nodeElement);
            });
            
            console.log('✅ Test Main App initialized successfully');
            
        } catch (error) {
            console.error('❌ Test Main App initialization failed:', error);
            alert('Test initialization failed: ' + error.message);
        }
    </script>
</body>
</html>
