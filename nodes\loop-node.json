{"id": "loop-node", "name": "Loop Iterator", "type": "loop", "version": "1.0.0", "description": "Iterates over arrays or repeats operations for a specified number of times", "metadata": {"category": "Control Flow", "tags": ["loop", "iteration", "repeat", "array"], "author": "Dynamic Workflow System", "documentation": "Processes arrays element by element or repeats operations with counter"}, "pins": {"input": {"count": 2, "dynamic": false, "definitions": [{"name": "data", "label": "Input Data", "dataType": "any", "required": true, "description": "Array to iterate over or data to repeat"}, {"name": "config", "label": "Loop Config", "dataType": "object", "required": false, "description": "Loop configuration (type: 'array' or 'count', count: number)"}]}, "output": {"count": 3, "dynamic": false, "definitions": [{"name": "item", "label": "Current Item", "dataType": "any", "description": "Current iteration item or data with index"}, {"name": "index", "label": "Current Index", "dataType": "number", "description": "Current iteration index"}, {"name": "complete", "label": "Loop Complete", "dataType": "array", "description": "All processed items when loop completes"}]}}, "visual": {"icon": {"type": "unicode", "value": "🔄", "color": "#2196F3"}, "shape": {"type": "circle"}, "sizing": {"baseWidth": 90, "baseHeight": 90, "pinSpacing": 22, "dynamicResize": false}, "colors": {"background": "#E3F2FD", "border": "#2196F3", "text": "#0D47A1"}}, "backend": {"endpoint": "/api/nodes/loop", "method": "POST", "code": "async function execute(req, res) {\n  try {\n    const { data, config } = req.body.inputs;\n    const loopConfig = config || { type: 'array' };\n    \n    const results = [];\n    \n    if (loopConfig.type === 'array' && Array.isArray(data)) {\n      // Iterate over array\n      for (let i = 0; i < data.length; i++) {\n        results.push({\n          item: data[i],\n          index: i,\n          isLast: i === data.length - 1\n        });\n      }\n    } else if (loopConfig.type === 'count' && loopConfig.count) {\n      // Repeat for count\n      for (let i = 0; i < loopConfig.count; i++) {\n        results.push({\n          item: { ...data, iteration: i },\n          index: i,\n          isLast: i === loopConfig.count - 1\n        });\n      }\n    }\n    \n    res.json({ \n      success: true, \n      outputs: { \n        iterations: results,\n        complete: results.map(r => r.item)\n      } \n    });\n  } catch (error) {\n    res.status(400).json({ success: false, error: error.message });\n  }\n}", "dependencies": [], "middleware": ["express.json()"]}, "frontend": {"executable": {"code": "class LoopNode {\n  constructor(nodeId, config) {\n    this.nodeId = nodeId;\n    this.config = config;\n    this.inputs = {};\n    this.outputs = {};\n    this.currentIndex = 0;\n    this.loopData = null;\n    this.results = [];\n  }\n  \n  async execute() {\n    if (!this.inputs.data) {\n      return; // Wait for input data\n    }\n    \n    try {\n      const data = this.inputs.data;\n      const config = this.inputs.config || { type: 'array' };\n      \n      this.results = [];\n      \n      if (config.type === 'array' && Array.isArray(data)) {\n        // Process array items\n        for (let i = 0; i < data.length; i++) {\n          this.sendOutput('item', data[i]);\n          this.sendOutput('index', i);\n          this.results.push(data[i]);\n          \n          // Small delay to allow processing\n          await new Promise(resolve => setTimeout(resolve, 10));\n        }\n      } else if (config.type === 'count' && config.count) {\n        // Repeat for count\n        for (let i = 0; i < config.count; i++) {\n          const itemData = { ...data, iteration: i };\n          this.sendOutput('item', itemData);\n          this.sendOutput('index', i);\n          this.results.push(itemData);\n          \n          // Small delay to allow processing\n          await new Promise(resolve => setTimeout(resolve, 10));\n        }\n      }\n      \n      // Send complete signal\n      this.sendOutput('complete', this.results);\n      \n      // Clear inputs for next execution\n      this.inputs = {};\n    } catch (error) {\n      console.error('Loop node error:', error);\n    }\n  }\n  \n  receiveInput(pinName, data) {\n    this.inputs[pinName] = data;\n    if (pinName === 'data') {\n      this.execute();\n    }\n  }\n  \n  sendOutput(pinName, data) {\n    if (this.outputs[pinName]) {\n      this.outputs[pinName].forEach(connection => {\n        connection.targetNode.receiveInput(connection.targetPin, data);\n      });\n    }\n  }\n}", "dependencies": []}, "properties": {"panel": [{"type": "select", "name": "loopType", "label": "Loop Type", "defaultValue": "array", "options": [{"value": "array", "label": "Iterate Array"}, {"value": "count", "label": "Repeat Count"}]}, {"type": "number", "name": "repeatCount", "label": "Repeat Count", "defaultValue": 5, "validation": {"min": 1, "max": 1000}}]}}}