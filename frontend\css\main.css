/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow: hidden;
}

/* App Layout */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
}

/* Header */
.app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 60px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    z-index: 1000;
}

.header-left .app-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-center .workflow-controls {
    display: flex;
    gap: 10px;
}

.header-right .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-connecting { background-color: var(--warning-color); }
.status-connected { background-color: var(--success-color); }
.status-disconnected { background-color: var(--error-color); }

/* Main Content */
.app-main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebars */
.sidebar {
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    min-height: 0;
}

.sidebar-left {
    width: 280px;
    border-right: 1px solid var(--border-color);
}

.sidebar-right {
    width: 320px;
    border-left: 1px solid var(--border-color);
}

.sidebar-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

/* Search Container */
.search-container {
    position: relative;
    margin-top: 10px;
}

.search-input {
    width: 100%;
    padding: 8px 12px 8px 35px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    background: var(--bg-secondary);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 12px;
}

/* Workflow Canvas Container */
.workflow-canvas-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    position: relative;
}

.canvas-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    height: 50px;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-center {
    flex: 1;
    text-align: center;
}

.canvas-info {
    font-size: 12px;
    color: var(--text-secondary);
    font-style: italic;
}

.zoom-level {
    font-size: 12px;
    color: var(--text-secondary);
    min-width: 40px;
    text-align: center;
}

/* Workflow Canvas */
.workflow-canvas {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: var(--bg-secondary);
}

.canvas-svg {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.nodes-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Footer */
.app-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 30px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    font-size: 12px;
    color: var(--text-secondary);
}

.footer-left,
.footer-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.separator {
    color: var(--border-color);
}

.execution-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 11px;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-light);
    border-color: var(--success-color);
}

.btn-info {
    background: var(--info-color);
    color: var(--text-light);
    border-color: var(--info-color);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-light);
    border-color: var(--warning-color);
}

.btn-ghost {
    background: transparent;
    border-color: transparent;
}

.btn-ghost:hover {
    background: var(--bg-primary);
}

.btn.active {
    background: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-overlay.hidden {
    display: none;
}

.modal-container {
    background: var(--bg-secondary);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-shadow: var(--shadow-hover);
    z-index: 9999;
    min-width: 150px;
}

.context-menu.hidden {
    display: none;
}

.context-menu-item {
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.context-menu-item:last-child {
    border-bottom: none;
}

.context-menu-item:hover {
    background: var(--bg-primary);
}

.context-menu-item.disabled {
    color: var(--text-secondary);
    cursor: not-allowed;
}

.context-menu-item.disabled:hover {
    background: transparent;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.text-center {
    text-align: center;
}

.text-muted {
    color: var(--text-secondary);
}

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }