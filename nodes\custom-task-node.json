{"id": "custom-task-node", "name": "Custom Task Processor", "type": "custom-task", "version": "1.0.0", "description": "Executes custom JavaScript code with configurable inputs and outputs", "metadata": {"category": "Processing", "tags": ["custom", "javascript", "transform", "processing"], "author": "Dynamic Workflow System", "documentation": "Allows users to define custom processing logic with JavaScript code"}, "pins": {"input": {"count": 1, "dynamic": true, "min": 1, "max": 5, "definitions": [{"name": "input1", "label": "Input 1", "dataType": "any", "required": true, "description": "Primary input data"}]}, "output": {"count": 1, "dynamic": true, "min": 1, "max": 5, "definitions": [{"name": "output1", "label": "Output 1", "dataType": "any", "description": "Processed output data"}]}}, "visual": {"icon": {"type": "unicode", "value": "⚙️", "color": "#9C27B0"}, "shape": {"type": "rectangle"}, "sizing": {"baseWidth": 120, "baseHeight": 70, "pinSpacing": 18, "dynamicResize": true}, "colors": {"background": "#F3E5F5", "border": "#9C27B0", "text": "#4A148C"}}, "backend": {"endpoint": "/api/nodes/custom-task", "method": "POST", "code": "async function execute(req, res) {\n  try {\n    const inputs = req.body.inputs;\n    const { customCode, outputMapping } = req.body.config;\n    \n    // Create execution context\n    const context = {\n      inputs,\n      console: {\n        log: (...args) => console.log('[Custom Task]', ...args),\n        error: (...args) => console.error('[Custom Task]', ...args)\n      },\n      Math,\n      Date,\n      JSON\n    };\n    \n    // Execute custom code safely\n    const executeCode = new Function(\n      'inputs', 'console', 'Math', 'Date', 'JSON',\n      `\n      ${customCode}\n      return typeof result !== 'undefined' ? result : inputs.input1;\n      `\n    );\n    \n    const result = executeCode(\n      context.inputs,\n      context.console,\n      context.Math,\n      context.Date,\n      context.JSON\n    );\n    \n    // Map result to outputs\n    const outputs = {};\n    if (outputMapping && Array.isArray(outputMapping)) {\n      outputMapping.forEach((mapping, index) => {\n        if (mapping.name) {\n          outputs[mapping.name] = Array.isArray(result) ? result[index] : result;\n        }\n      });\n    } else {\n      outputs.output1 = result;\n    }\n    \n    res.json({ success: true, outputs });\n  } catch (error) {\n    res.status(400).json({ success: false, error: error.message });\n  }\n}", "dependencies": [], "middleware": ["express.json()"]}, "frontend": {"executable": {"code": "class CustomTaskNode {\n  constructor(nodeId, config) {\n    this.nodeId = nodeId;\n    this.config = config;\n    this.inputs = {};\n    this.outputs = {};\n    this.customCode = config.customCode || 'return inputs.input1;';\n    this.requiredInputs = config.requiredInputs || ['input1'];\n  }\n  \n  async execute() {\n    // Check if all required inputs are available\n    const hasAllInputs = this.requiredInputs.every(inputName => \n      this.inputs.hasOwnProperty(inputName)\n    );\n    \n    if (!hasAllInputs) {\n      return; // Wait for all required inputs\n    }\n    \n    try {\n      // Create safe execution context\n      const context = {\n        inputs: this.inputs,\n        console: {\n          log: (...args) => console.log(`[${this.nodeId}]`, ...args),\n          error: (...args) => console.error(`[${this.nodeId}]`, ...args)\n        },\n        Math,\n        Date,\n        JSON\n      };\n      \n      // Execute custom code\n      const executeCode = new Function(\n        'inputs', 'console', 'Math', 'Date', 'JSON',\n        `\n        ${this.customCode}\n        return typeof result !== 'undefined' ? result : inputs.input1;\n        `\n      );\n      \n      const result = executeCode(\n        context.inputs,\n        context.console,\n        context.Math,\n        context.Date,\n        context.JSON\n      );\n      \n      // Send to outputs\n      if (this.config.outputMapping && Array.isArray(this.config.outputMapping)) {\n        this.config.outputMapping.forEach((mapping, index) => {\n          if (mapping.name) {\n            const outputData = Array.isArray(result) ? result[index] : result;\n            this.sendOutput(mapping.name, outputData);\n          }\n        });\n      } else {\n        this.sendOutput('output1', result);\n      }\n      \n      // Clear inputs for next execution\n      this.inputs = {};\n    } catch (error) {\n      console.error('Custom task node error:', error);\n    }\n  }\n  \n  receiveInput(pinName, data) {\n    this.inputs[pinName] = data;\n    this.execute();\n  }\n  \n  sendOutput(pinName, data) {\n    if (this.outputs[pinName]) {\n      this.outputs[pinName].forEach(connection => {\n        connection.targetNode.receiveInput(connection.targetPin, data);\n      });\n    }\n  }\n}", "dependencies": []}, "properties": {"panel": [{"type": "textarea", "name": "customCode", "label": "Custom JavaScript Code", "defaultValue": "// Process the input data\nconst result = inputs.input1;\n\n// Transform or manipulate as needed\n// Available: inputs, console, Math, Date, JSON\n\nreturn result;", "validation": {"required": true}}, {"type": "number", "name": "inputCount", "label": "Number of Inputs", "defaultValue": 1, "validation": {"min": 1, "max": 5}}, {"type": "number", "name": "outputCount", "label": "Number of Outputs", "defaultValue": 1, "validation": {"min": 1, "max": 5}}, {"type": "text", "name": "taskName", "label": "Task Name", "defaultValue": "Custom Task"}]}}}