<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Workflow System</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔀</text></svg>">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
            height: 100vh;
        }
        
        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        /* Header */
        .app-header {
            background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
            border-bottom: 1px solid #333;
            padding: 12px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        
        .header-left h1 {
            font-size: 20px;
            font-weight: 600;
            color: #4a90e2;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .header-center {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn-primary {
            background: #4a90e2;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5aa3f0;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #3a3a3a;
            color: #ccc;
            border: 1px solid #555;
        }
        
        .btn-secondary:hover {
            background: #4a4a4a;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #34ce57;
        }
        
        .connection-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            background: #28a745;
            color: white;
        }
        
        /* Main Content */
        .app-main {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        /* Sidebar */
        .sidebar {
            width: 280px;
            background: #2a2a2a;
            border-right: 1px solid #333;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #333;
        }
        
        .sidebar-header h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #e0e0e0;
        }
        
        .search-input {
            width: 100%;
            padding: 8px 12px;
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 6px;
            color: white;
            font-size: 14px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #4a90e2;
        }
        
        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .node-category {
            margin-bottom: 24px;
        }
        
        .node-category h4 {
            font-size: 14px;
            font-weight: 600;
            color: #888;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .node-item {
            background: #3a3a3a;
            border: 1px solid #555;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
        }
        
        .node-item:hover {
            background: #4a4a4a;
            border-color: #4a90e2;
            transform: translateX(4px);
        }
        
        .node-item-icon {
            font-size: 20px;
            width: 24px;
            text-align: center;
        }
        
        .node-item-content {
            flex: 1;
        }
        
        .node-item-name {
            font-weight: 500;
            color: #e0e0e0;
            font-size: 14px;
        }
        
        /* Canvas Area */
        .canvas-container {
            flex: 1;
            position: relative;
            background: #333;
            overflow: hidden;
        }
        
        .canvas-controls {
            position: absolute;
            top: 16px;
            left: 16px;
            z-index: 100;
            display: flex;
            gap: 8px;
            align-items: center;
            background: rgba(0,0,0,0.8);
            padding: 8px 12px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }
        
        .zoom-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            background: #4a4a4a;
            color: white;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.2s ease;
        }
        
        .zoom-btn:hover {
            background: #4a90e2;
        }
        
        .zoom-display {
            padding: 4px 8px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 12px;
            color: #ccc;
            min-width: 50px;
            text-align: center;
        }
        
        .workflow-canvas {
            width: 100%;
            height: 100%;
            position: relative;
            background: 
                radial-gradient(circle at 1px 1px, #444 1px, transparent 0);
            background-size: 20px 20px;
        }
        
        .nodes-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transform-origin: 0 0;
        }
        
        /* Node Styles */
        .workflow-node {
            position: absolute;
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            border: 2px solid #5aa3f0;
            border-radius: 12px;
            padding: 16px;
            cursor: move;
            user-select: none;
            min-width: 140px;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transition: all 0.2s ease;
        }
        
        .workflow-node:hover {
            background: linear-gradient(135deg, #5aa3f0 0%, #4a90e2 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3);
        }
        
        .workflow-node.dragging {
            opacity: 0.9;
            z-index: 1000;
            transform: rotate(2deg) scale(1.05);
        }
        
        .node-icon {
            font-size: 28px;
            margin-bottom: 8px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }
        
        .node-title {
            font-weight: 600;
            font-size: 13px;
            text-align: center;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        
        /* Properties Panel */
        .properties-panel {
            width: 300px;
            background: #2a2a2a;
            border-left: 1px solid #333;
            display: flex;
            flex-direction: column;
        }
        
        .properties-header {
            padding: 20px;
            border-bottom: 1px solid #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .properties-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .no-selection {
            text-align: center;
            color: #888;
            font-style: italic;
            margin-top: 40px;
        }
        
        /* Footer */
        .app-footer {
            background: #1e1e1e;
            border-top: 1px solid #333;
            padding: 8px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #888;
        }
        
        .footer-left {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .separator {
            color: #555;
        }
        
        .status-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 500;
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }
        
        /* Animations */
        @keyframes nodeCreate {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(20px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
        
        .workflow-node.creating {
            animation: nodeCreate 0.3s ease-out;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <h1>🔀 Dynamic Workflow System</h1>
            </div>
            <div class="header-center">
                <button class="btn btn-primary" onclick="newWorkflow()">
                    ➕ New Workflow
                </button>
                <button class="btn btn-secondary" onclick="saveWorkflow()">
                    💾 Save
                </button>
                <button class="btn btn-secondary" onclick="loadWorkflow()">
                    📁 Load
                </button>
                <button class="btn btn-success" onclick="executeWorkflow()">
                    ▶️ Execute
                </button>
            </div>
            <div class="header-right">
                <div class="connection-status">Connected</div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Left Sidebar - Node Palette -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3>Node Palette</h3>
                    <input type="text" class="search-input" placeholder="Search nodes..." 
                           oninput="filterNodes(this.value)">
                </div>
                <div class="sidebar-content" id="nodePalette">
                    <div class="node-category">
                        <h4>Control Flow</h4>
                        <div class="node-item" onclick="createNode('conditional', '🔀', 'Conditional Decision')">
                            <span class="node-item-icon">🔀</span>
                            <div class="node-item-content">
                                <div class="node-item-name">Conditional Decision</div>
                            </div>
                        </div>
                        <div class="node-item" onclick="createNode('loop', '🔄', 'Loop Iterator')">
                            <span class="node-item-icon">🔄</span>
                            <div class="node-item-content">
                                <div class="node-item-name">Loop Iterator</div>
                            </div>
                        </div>
                        <div class="node-item" onclick="createNode('branch', '🌿', 'Multi-Branch Router')">
                            <span class="node-item-icon">🌿</span>
                            <div class="node-item-content">
                                <div class="node-item-name">Multi-Branch Router</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="node-category">
                        <h4>Processing</h4>
                        <div class="node-item" onclick="createNode('processor', '⚙️', 'Custom Task Processor')">
                            <span class="node-item-icon">⚙️</span>
                            <div class="node-item-content">
                                <div class="node-item-name">Custom Task Processor</div>
                            </div>
                        </div>
                        <div class="node-item" onclick="createNode('transform', '🔄', 'Data Transformer')">
                            <span class="node-item-icon">🔄</span>
                            <div class="node-item-content">
                                <div class="node-item-name">Data Transformer</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="node-category">
                        <h4>Input/Output</h4>
                        <div class="node-item" onclick="createNode('input', '📥', 'Data Input')">
                            <span class="node-item-icon">📥</span>
                            <div class="node-item-content">
                                <div class="node-item-name">Data Input</div>
                            </div>
                        </div>
                        <div class="node-item" onclick="createNode('output', '📤', 'Data Output')">
                            <span class="node-item-icon">📤</span>
                            <div class="node-item-content">
                                <div class="node-item-name">Data Output</div>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Center - Canvas Area -->
            <div class="canvas-container">
                <div class="canvas-controls">
                    <button class="zoom-btn" onclick="zoomOut()" title="Zoom Out">−</button>
                    <button class="zoom-btn" onclick="zoomIn()" title="Zoom In">+</button>
                    <button class="zoom-btn" onclick="resetZoom()" title="Reset Zoom">⌂</button>
                    <div class="zoom-display" id="zoomDisplay">100%</div>
                </div>
                
                <div class="workflow-canvas" id="canvas">
                    <div class="nodes-overlay" id="nodesOverlay"></div>
                </div>
            </div>

            <!-- Right Sidebar - Properties Panel -->
            <aside class="properties-panel">
                <div class="properties-header">
                    <h3>Properties</h3>
                    <button class="btn btn-secondary" onclick="closeProperties()" style="padding: 4px 8px;">×</button>
                </div>
                <div class="properties-content" id="propertiesContent">
                    <div class="no-selection">
                        <p>Select a node to view its properties</p>
                    </div>
                </div>
            </aside>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-left">
                <span id="nodeCount">0 nodes</span>
                <span class="separator">|</span>
                <span id="connectionCount">0 connections</span>
            </div>
            <div class="footer-center">
                <span class="status-indicator" id="appStatus">Ready</span>
            </div>
            <div class="footer-right">
                <span>v2.0.0</span>
            </div>
        </footer>
    </div>
    <script>
        // 全局状态管理
        const AppState = {
            zoom: 1,
            pan: { x: 0, y: 0 },
            nodeCounter: 0,
            selectedNode: null,
            isDragging: false,
            dragTarget: null,
            dragOffset: { x: 0, y: 0 },
            nodes: new Map(),
            connections: []
        };

        // DOM 元素引用
        const Elements = {
            canvas: document.getElementById('canvas'),
            nodesOverlay: document.getElementById('nodesOverlay'),
            zoomDisplay: document.getElementById('zoomDisplay'),
            nodeCount: document.getElementById('nodeCount'),
            connectionCount: document.getElementById('connectionCount'),
            appStatus: document.getElementById('appStatus'),
            propertiesContent: document.getElementById('propertiesContent')
        };

        // 工具函数
        function log(message, type = 'info') {
            console.log(`🔀 Workflow [${type.toUpperCase()}]:`, message);
            Elements.appStatus.textContent = message;

            // 状态指示器颜色
            Elements.appStatus.className = 'status-indicator';
            if (type === 'error') Elements.appStatus.style.background = 'rgba(220, 53, 69, 0.2)';
            else if (type === 'success') Elements.appStatus.style.background = 'rgba(40, 167, 69, 0.2)';
            else Elements.appStatus.style.background = 'rgba(74, 144, 226, 0.2)';
        }

        function updateTransform() {
            const transform = `translate(${AppState.pan.x}px, ${AppState.pan.y}px) scale(${AppState.zoom})`;
            Elements.nodesOverlay.style.transform = transform;
            Elements.zoomDisplay.textContent = Math.round(AppState.zoom * 100) + '%';

            log(`Zoom: ${Math.round(AppState.zoom * 100)}%`);
        }

        function updateCounters() {
            Elements.nodeCount.textContent = `${AppState.nodes.size} nodes`;
            Elements.connectionCount.textContent = `${AppState.connections.length} connections`;
        }

        // 节点创建函数
        function createNode(type, icon, name) {
            AppState.nodeCounter++;
            const nodeId = `node_${type}_${AppState.nodeCounter}_${Date.now()}`;

            const node = document.createElement('div');
            node.className = 'workflow-node creating';
            node.id = nodeId;
            node.innerHTML = `
                <div class="node-icon">${icon}</div>
                <div class="node-title">${name} ${AppState.nodeCounter}</div>
            `;

            // 设置初始位置（避免重叠）
            const x = 150 + (AppState.nodeCounter % 5) * 180;
            const y = 100 + Math.floor(AppState.nodeCounter / 5) * 120;
            node.style.left = x + 'px';
            node.style.top = y + 'px';

            // 存储节点数据
            AppState.nodes.set(nodeId, {
                id: nodeId,
                type: type,
                name: name,
                icon: icon,
                position: { x, y },
                element: node
            });

            // 设置拖动和点击事件
            setupNodeInteractions(node, nodeId);

            // 添加到画布
            Elements.nodesOverlay.appendChild(node);

            // 移除创建动画类
            setTimeout(() => node.classList.remove('creating'), 300);

            updateCounters();
            log(`Created ${name} ${AppState.nodeCounter}`, 'success');
        }

        // 节点交互设置
        function setupNodeInteractions(node, nodeId) {
            // 点击选择
            node.addEventListener('click', (e) => {
                e.stopPropagation();
                selectNode(nodeId);
            });

            // 拖动开始
            node.addEventListener('mousedown', (e) => {
                if (e.button !== 0) return; // 只响应左键

                AppState.isDragging = true;
                AppState.dragTarget = node;

                const rect = node.getBoundingClientRect();
                const canvasRect = Elements.canvas.getBoundingClientRect();

                // 计算拖动偏移，考虑缩放
                AppState.dragOffset.x = (e.clientX - rect.left) / AppState.zoom;
                AppState.dragOffset.y = (e.clientY - rect.top) / AppState.zoom;

                node.classList.add('dragging');
                selectNode(nodeId);

                e.preventDefault();
                e.stopPropagation();
            });
        }

        // 节点选择
        function selectNode(nodeId) {
            // 清除之前的选择
            if (AppState.selectedNode) {
                const prevNode = AppState.nodes.get(AppState.selectedNode);
                if (prevNode && prevNode.element) {
                    prevNode.element.style.borderColor = '#5aa3f0';
                }
            }

            // 选择新节点
            AppState.selectedNode = nodeId;
            const nodeData = AppState.nodes.get(nodeId);

            if (nodeData) {
                nodeData.element.style.borderColor = '#ffd700';
                showNodeProperties(nodeData);
                log(`Selected ${nodeData.name}`, 'info');
            }
        }

        // 显示节点属性
        function showNodeProperties(nodeData) {
            Elements.propertiesContent.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #4a90e2; margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <span style="font-size: 20px;">${nodeData.icon}</span>
                        ${nodeData.name}
                    </h4>
                    <div style="background: #1a1a1a; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
                        <div style="font-size: 12px; color: #888; margin-bottom: 4px;">Node ID</div>
                        <div style="font-family: monospace; font-size: 11px; color: #ccc;">${nodeData.id}</div>
                    </div>
                    <div style="background: #1a1a1a; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
                        <div style="font-size: 12px; color: #888; margin-bottom: 4px;">Type</div>
                        <div style="color: #e0e0e0;">${nodeData.type}</div>
                    </div>
                    <div style="background: #1a1a1a; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
                        <div style="font-size: 12px; color: #888; margin-bottom: 4px;">Position</div>
                        <div style="color: #e0e0e0;">X: ${Math.round(nodeData.position.x)}, Y: ${Math.round(nodeData.position.y)}</div>
                    </div>
                </div>
                <div style="border-top: 1px solid #333; padding-top: 16px;">
                    <button class="btn btn-secondary" onclick="deleteSelectedNode()" style="width: 100%; background: #dc3545; border-color: #dc3545;">
                        🗑️ Delete Node
                    </button>
                </div>
            `;
        }

        // 全局鼠标事件
        document.addEventListener('mousemove', (e) => {
            if (AppState.isDragging && AppState.dragTarget) {
                const canvasRect = Elements.canvas.getBoundingClientRect();

                // 计算新位置，考虑缩放和平移
                const x = (e.clientX - canvasRect.left - AppState.pan.x) / AppState.zoom - AppState.dragOffset.x;
                const y = (e.clientY - canvasRect.top - AppState.pan.y) / AppState.zoom - AppState.dragOffset.y;

                // 限制在画布范围内
                const clampedX = Math.max(0, Math.min(x, (Elements.canvas.clientWidth / AppState.zoom) - 140));
                const clampedY = Math.max(0, Math.min(y, (Elements.canvas.clientHeight / AppState.zoom) - 80));

                AppState.dragTarget.style.left = clampedX + 'px';
                AppState.dragTarget.style.top = clampedY + 'px';

                // 更新节点数据
                const nodeId = AppState.dragTarget.id;
                const nodeData = AppState.nodes.get(nodeId);
                if (nodeData) {
                    nodeData.position.x = clampedX;
                    nodeData.position.y = clampedY;

                    // 如果是选中的节点，更新属性面板
                    if (AppState.selectedNode === nodeId) {
                        showNodeProperties(nodeData);
                    }
                }
            }
        });

        document.addEventListener('mouseup', () => {
            if (AppState.isDragging && AppState.dragTarget) {
                AppState.dragTarget.classList.remove('dragging');
                log(`Moved ${AppState.dragTarget.querySelector('.node-title').textContent}`, 'success');
                AppState.isDragging = false;
                AppState.dragTarget = null;
            }
        });

        // 画布点击（取消选择）
        Elements.canvas.addEventListener('click', (e) => {
            if (e.target === Elements.canvas || e.target === Elements.nodesOverlay) {
                if (AppState.selectedNode) {
                    const nodeData = AppState.nodes.get(AppState.selectedNode);
                    if (nodeData && nodeData.element) {
                        nodeData.element.style.borderColor = '#5aa3f0';
                    }
                    AppState.selectedNode = null;
                    Elements.propertiesContent.innerHTML = '<div class="no-selection"><p>Select a node to view its properties</p></div>';
                    log('Selection cleared', 'info');
                }
            }
        });

        // 缩放功能
        function zoomIn() {
            AppState.zoom = Math.min(AppState.zoom * 1.2, 3);
            updateTransform();
        }

        function zoomOut() {
            AppState.zoom = Math.max(AppState.zoom * 0.8, 0.2);
            updateTransform();
        }

        function resetZoom() {
            AppState.zoom = 1;
            AppState.pan = { x: 0, y: 0 };
            updateTransform();
            log('Zoom reset', 'info');
        }

        // 鼠标滚轮缩放
        Elements.canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            AppState.zoom = Math.max(0.2, Math.min(3, AppState.zoom * delta));
            updateTransform();
        });

        // 工具栏功能
        function newWorkflow() {
            if (AppState.nodes.size > 0) {
                if (confirm('Create new workflow? This will clear the current workflow.')) {
                    clearWorkflow();
                }
            }
            log('New workflow created', 'success');
        }

        function saveWorkflow() {
            const workflow = {
                nodes: Array.from(AppState.nodes.values()).map(node => ({
                    id: node.id,
                    type: node.type,
                    name: node.name,
                    icon: node.icon,
                    position: node.position
                })),
                connections: AppState.connections,
                zoom: AppState.zoom,
                pan: AppState.pan
            };

            const dataStr = JSON.stringify(workflow, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `workflow_${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.json`;
            link.click();
            URL.revokeObjectURL(url);

            log('Workflow saved', 'success');
        }

        function loadWorkflow() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const workflow = JSON.parse(e.target.result);
                            loadWorkflowData(workflow);
                            log('Workflow loaded', 'success');
                        } catch (error) {
                            log('Failed to load workflow', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function loadWorkflowData(workflow) {
            clearWorkflow();

            // 恢复缩放和平移
            AppState.zoom = workflow.zoom || 1;
            AppState.pan = workflow.pan || { x: 0, y: 0 };
            updateTransform();

            // 重建节点
            workflow.nodes.forEach(nodeData => {
                AppState.nodeCounter++;
                const node = document.createElement('div');
                node.className = 'workflow-node';
                node.id = nodeData.id;
                node.innerHTML = `
                    <div class="node-icon">${nodeData.icon}</div>
                    <div class="node-title">${nodeData.name}</div>
                `;

                node.style.left = nodeData.position.x + 'px';
                node.style.top = nodeData.position.y + 'px';

                AppState.nodes.set(nodeData.id, {
                    ...nodeData,
                    element: node
                });

                setupNodeInteractions(node, nodeData.id);
                Elements.nodesOverlay.appendChild(node);
            });

            updateCounters();
        }

        function executeWorkflow() {
            if (AppState.nodes.size === 0) {
                log('No nodes to execute', 'error');
                return;
            }

            log('Executing workflow...', 'info');

            // 模拟执行过程
            let executed = 0;
            const total = AppState.nodes.size;

            AppState.nodes.forEach((nodeData, nodeId) => {
                setTimeout(() => {
                    nodeData.element.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                    executed++;

                    if (executed === total) {
                        setTimeout(() => {
                            AppState.nodes.forEach(node => {
                                node.element.style.background = 'linear-gradient(135deg, #4a90e2 0%, #357abd 100%)';
                            });
                            log('Workflow execution completed', 'success');
                        }, 1000);
                    }
                }, executed * 200);
            });
        }

        function clearWorkflow() {
            Elements.nodesOverlay.innerHTML = '';
            AppState.nodes.clear();
            AppState.connections = [];
            AppState.nodeCounter = 0;
            AppState.selectedNode = null;
            Elements.propertiesContent.innerHTML = '<div class="no-selection"><p>Select a node to view its properties</p></div>';
            updateCounters();
        }

        function deleteSelectedNode() {
            if (AppState.selectedNode) {
                const nodeData = AppState.nodes.get(AppState.selectedNode);
                if (nodeData) {
                    nodeData.element.remove();
                    AppState.nodes.delete(AppState.selectedNode);
                    AppState.selectedNode = null;
                    Elements.propertiesContent.innerHTML = '<div class="no-selection"><p>Select a node to view its properties</p></div>';
                    updateCounters();
                    log('Node deleted', 'success');
                }
            }
        }

        function closeProperties() {
            if (AppState.selectedNode) {
                const nodeData = AppState.nodes.get(AppState.selectedNode);
                if (nodeData && nodeData.element) {
                    nodeData.element.style.borderColor = '#5aa3f0';
                }
                AppState.selectedNode = null;
                Elements.propertiesContent.innerHTML = '<div class="no-selection"><p>Select a node to view its properties</p></div>';
                log('Properties panel closed', 'info');
            }
        }

        function filterNodes(searchTerm) {
            const items = document.querySelectorAll('.node-item');
            const term = searchTerm.toLowerCase();

            items.forEach(item => {
                const name = item.querySelector('.node-item-name').textContent.toLowerCase();
                if (name.includes(term)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.target.tagName === 'INPUT') return;

            switch (e.key) {
                case 'Delete':
                case 'Backspace':
                    if (AppState.selectedNode) {
                        deleteSelectedNode();
                    }
                    break;
                case 'Escape':
                    closeProperties();
                    break;
                case '=':
                case '+':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        zoomIn();
                    }
                    break;
                case '-':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        zoomOut();
                    }
                    break;
                case '0':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        resetZoom();
                    }
                    break;
            }
        });

        // 初始化
        updateTransform();
        updateCounters();
        log('Dynamic Workflow System v2.0 Ready', 'success');
    </script>
</body>
</html>
