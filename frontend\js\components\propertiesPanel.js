﻿/**
 * Properties Panel Component for Dynamic Workflow System
 * Renders dynamic configuration UI based on node JSON definitions
 */

class PropertiesPanel {
  constructor(element, eventEmitter) {
    this.element = element;
    this.eventEmitter = eventEmitter;
    this.currentNode = null;
    this.currentConfig = {};
    this.validators = new Map();
    this.formElements = new Map();
    this.nodeManager = null; // Will be set by app

    this.setupEventListeners();
    this.hide(); // Start with no selection message
  }

  setupEventListeners() {
    // Node selection events
    this.eventEmitter.on('node:select', (nodeId) => {
      this.showNodeProperties(nodeId);
    });

    this.eventEmitter.on('selection:changed', (selectedNodes) => {
      if (selectedNodes.length === 0) {
        this.hide();
      } else if (selectedNodes.length === 1) {
        this.showNodeProperties(selectedNodes[0]);
      } else {
        this.showMultiSelection(selectedNodes);
      }
    });

    // Configuration changes
    this.eventEmitter.on('node:config:changed', (data) => {
      if (this.currentNode && this.currentNode.id === data.nodeId) {
        this.currentConfig = data.config;
        this.updateFormValues();
      }
    });
  }

  setNodeManager(nodeManager) {
    this.nodeManager = nodeManager;
  }

  showNodeProperties(nodeId) {
    if (window.workflowDebugger) {
      window.workflowDebugger.log('info', 'PropertiesPanel: showNodeProperties called', {
        nodeId: nodeId,
        nodeIdType: typeof nodeId,
        nodeManager: !!this.nodeManager,
        element: !!this.element
      });
    }

    console.log('PropertiesPanel: showNodeProperties called with nodeId:', nodeId);

    if (!this.nodeManager) {
      console.error('NodeManager not set in PropertiesPanel');
      if (window.workflowDebugger) {
        window.workflowDebugger.log('error', 'NodeManager not set in PropertiesPanel');
      }
      return;
    }

    const node = this.nodeManager.getNode(nodeId);
    if (node) {
      console.log('PropertiesPanel: Found node:', node.name);
      if (window.workflowDebugger) {
        window.workflowDebugger.log('success', 'PropertiesPanel: Found node', {
          nodeId: node.id,
          nodeName: node.name,
          nodeType: node.type
        });
      }
      this.currentNode = node;
      this.currentConfig = node.config || {};
      this.renderNodeProperties(node);
    } else {
      console.error('PropertiesPanel: Node not found:', nodeId);
      if (window.workflowDebugger) {
        window.workflowDebugger.log('error', 'PropertiesPanel: Node not found', {
          requestedNodeId: nodeId,
          availableNodes: this.nodeManager.getAllNodes().map(n => n.id)
        });
      }
    }
  }

  renderNodeProperties(node) {
    this.element.innerHTML = '';

    // Create node info section
    const nodeInfo = this.createNodeInfoSection(node);
    this.element.appendChild(nodeInfo);

    // Create properties form
    if (node.frontend && node.frontend.properties && node.frontend.properties.panel) {
      const propertiesForm = this.createPropertiesForm(node.frontend.properties.panel);
      this.element.appendChild(propertiesForm);
    }

    // Create pin configuration section
    const pinConfig = this.createPinConfigSection(node);
    this.element.appendChild(pinConfig);

    // Create advanced section
    const advancedSection = this.createAdvancedSection(node);
    this.element.appendChild(advancedSection);
  }

  createNodeInfoSection(node) {
    const section = document.createElement('div');
    section.className = 'node-info';

    const header = document.createElement('div');
    header.className = 'node-info-header';

    const icon = document.createElement('span');
    icon.className = 'node-info-icon';
    if (node.visual.icon.type === 'unicode') {
      icon.textContent = node.visual.icon.value;
    } else if (node.visual.icon.type === 'fontawesome') {
      icon.className += ` ${node.visual.icon.value}`;
    }
    if (node.visual.icon.color) {
      icon.style.color = node.visual.icon.color;
    }

    const titleContainer = document.createElement('div');

    const title = document.createElement('div');
    title.className = 'node-info-title';
    title.textContent = node.name;

    const type = document.createElement('div');
    type.className = 'node-info-type';
    type.textContent = node.type;

    titleContainer.appendChild(title);
    titleContainer.appendChild(type);

    header.appendChild(icon);
    header.appendChild(titleContainer);

    const description = document.createElement('div');
    description.className = 'node-info-description';
    description.textContent = node.description || 'No description available';

    section.appendChild(header);
    section.appendChild(description);

    return section;
  }

  createPropertiesForm(panelDefinition) {
    const form = document.createElement('div');
    form.className = 'properties-form';

    // Group properties by category if needed
    const groups = this.groupProperties(panelDefinition);

    Object.entries(groups).forEach(([groupName, properties]) => {
      const group = this.createPropertyGroup(groupName, properties);
      form.appendChild(group);
    });

    return form;
  }

  groupProperties(properties) {
    // For now, put all properties in a single "Configuration" group
    // This could be extended to support property grouping
    return {
      'Configuration': properties
    };
  }

  createPropertyGroup(groupName, properties) {
    const group = document.createElement('div');
    group.className = 'property-group';

    const header = document.createElement('div');
    header.className = 'property-group-header';
    header.innerHTML = `
      <span>${groupName}</span>
      <i class="fas fa-chevron-down toggle-icon"></i>
    `;

    const content = document.createElement('div');
    content.className = 'property-group-content';

    // Add toggle functionality
    header.addEventListener('click', () => {
      group.classList.toggle('collapsed');
    });

    // Create property items
    properties.forEach(propDef => {
      const propertyItem = this.createPropertyItem(propDef);
      content.appendChild(propertyItem);
    });

    group.appendChild(header);
    group.appendChild(content);

    return group;
  }

  createPropertyItem(propDef) {
    const item = document.createElement('div');
    item.className = 'property-item';
    item.dataset.propertyName = propDef.name;

    // Label
    const label = document.createElement('label');
    label.className = 'property-label';
    if (propDef.validation && propDef.validation.required) {
      label.classList.add('required');
    }
    label.textContent = propDef.label || propDef.name;

    // Input element
    const input = this.createInputElement(propDef);
    this.formElements.set(propDef.name, input);

    // Description
    if (propDef.description) {
      const description = document.createElement('div');
      description.className = 'property-description';
      description.textContent = propDef.description;
      item.appendChild(description);
    }

    item.appendChild(label);
    item.appendChild(input);

    // Setup validation
    this.setupPropertyValidation(propDef, input, item);

    // Setup change handler
    this.setupPropertyChangeHandler(propDef, input);

    return item;
  }

  createInputElement(propDef) {
    const currentValue = this.currentConfig[propDef.name] ?? propDef.defaultValue;

    switch (propDef.type) {
      case 'text':
        return this.createTextInput(propDef, currentValue);
      case 'number':
        return this.createNumberInput(propDef, currentValue);
      case 'boolean':
        return this.createBooleanInput(propDef, currentValue);
      case 'select':
        return this.createSelectInput(propDef, currentValue);
      case 'textarea':
        return this.createTextareaInput(propDef, currentValue);
      case 'color':
        return this.createColorInput(propDef, currentValue);
      case 'file':
        return this.createFileInput(propDef, currentValue);
      default:
        return this.createTextInput(propDef, currentValue);
    }
  }

  createTextInput(propDef, value) {
    const input = document.createElement('input');
    input.type = 'text';
    input.className = 'property-input';
    input.value = value || '';
    input.placeholder = propDef.placeholder || '';

    if (propDef.validation) {
      if (propDef.validation.pattern) {
        input.pattern = propDef.validation.pattern;
      }
      if (propDef.validation.required) {
        input.required = true;
      }
    }

    return input;
  }

  createNumberInput(propDef, value) {
    const input = document.createElement('input');
    input.type = 'number';
    input.className = 'property-input';
    input.value = value !== undefined ? value : '';

    if (propDef.validation) {
      if (propDef.validation.min !== undefined) {
        input.min = propDef.validation.min;
      }
      if (propDef.validation.max !== undefined) {
        input.max = propDef.validation.max;
      }
      if (propDef.validation.required) {
        input.required = true;
      }
    }

    return input;
  }

  createBooleanInput(propDef, value) {
    const wrapper = document.createElement('div');
    wrapper.className = 'property-checkbox-wrapper';

    const input = document.createElement('input');
    input.type = 'checkbox';
    input.className = 'property-checkbox';
    input.checked = Boolean(value);

    const label = document.createElement('span');
    label.textContent = propDef.label || propDef.name;

    wrapper.appendChild(input);
    wrapper.appendChild(label);

    // Make the wrapper clickable
    wrapper.addEventListener('click', (e) => {
      if (e.target !== input) {
        input.checked = !input.checked;
        input.dispatchEvent(new Event('change'));
      }
    });

    return wrapper;
  }

  createSelectInput(propDef, value) {
    const select = document.createElement('select');
    select.className = 'property-input property-select';

    if (propDef.options && Array.isArray(propDef.options)) {
      propDef.options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.label || option.value;

        if (option.value === value) {
          optionElement.selected = true;
        }

        select.appendChild(optionElement);
      });
    }

    return select;
  }

  createTextareaInput(propDef, value) {
    const textarea = document.createElement('textarea');
    textarea.className = 'property-input property-textarea';
    textarea.value = value || '';
    textarea.placeholder = propDef.placeholder || '';
    textarea.rows = propDef.rows || 4;

    if (propDef.validation && propDef.validation.required) {
      textarea.required = true;
    }

    return textarea;
  }

  createColorInput(propDef, value) {
    const input = document.createElement('input');
    input.type = 'color';
    input.className = 'property-color';
    input.value = value || '#000000';

    return input;
  }

  createFileInput(propDef, value) {
    const input = document.createElement('input');
    input.type = 'file';
    input.className = 'property-input property-file';

    if (propDef.accept) {
      input.accept = propDef.accept;
    }

    return input;
  }

  setupPropertyValidation(propDef, input, item) {
    if (!propDef.validation) return;

    const validator = (value) => {
      const errors = [];

      if (propDef.validation.required && (!value || value.toString().trim() === '')) {
        errors.push('This field is required');
      }

      if (propDef.validation.min !== undefined && Number(value) < propDef.validation.min) {
        errors.push(`Value must be at least ${propDef.validation.min}`);
      }

      if (propDef.validation.max !== undefined && Number(value) > propDef.validation.max) {
        errors.push(`Value must be at most ${propDef.validation.max}`);
      }

      if (propDef.validation.pattern && value && !new RegExp(propDef.validation.pattern).test(value)) {
        errors.push('Invalid format');
      }

      return errors;
    };

    this.validators.set(propDef.name, validator);

    // Real-time validation
    const validateInput = () => {
      const value = this.getInputValue(input);
      const errors = validator(value);

      this.showValidationErrors(item, errors);
    };

    input.addEventListener('blur', validateInput);
    input.addEventListener('input', () => {
      // Clear errors on input
      this.clearValidationErrors(item);
    });
  }

  setupPropertyChangeHandler(propDef, input) {
    const handleChange = () => {
      const value = this.getInputValue(input);
      this.updateProperty(propDef.name, value);
    };

    if (input.type === 'checkbox') {
      input.addEventListener('change', handleChange);
    } else {
      input.addEventListener('input', handleChange);
      input.addEventListener('change', handleChange);
    }
  }

  getInputValue(input) {
    if (input.type === 'checkbox') {
      return input.checked;
    } else if (input.type === 'number') {
      return input.value === '' ? undefined : Number(input.value);
    } else if (input.type === 'file') {
      return input.files[0] || null;
    } else {
      return input.value;
    }
  }

  updateProperty(propertyName, value) {
    if (!this.currentNode) return;

    this.currentConfig[propertyName] = value;

    // Emit configuration change
    this.eventEmitter.emit('node:config:update', {
      nodeId: this.currentNode.id,
      property: propertyName,
      value: value,
      config: this.currentConfig
    });
  }

  updateFormValues() {
    this.formElements.forEach((input, propertyName) => {
      const value = this.currentConfig[propertyName];
      this.setInputValue(input, value);
    });
  }

  setInputValue(input, value) {
    if (input.type === 'checkbox') {
      input.checked = Boolean(value);
    } else if (value !== undefined) {
      input.value = value;
    }
  }

  showValidationErrors(item, errors) {
    this.clearValidationErrors(item);

    if (errors.length > 0) {
      item.classList.add('error');

      const errorDiv = document.createElement('div');
      errorDiv.className = 'property-error';
      errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${errors[0]}`;

      item.appendChild(errorDiv);
    }
  }

  clearValidationErrors(item) {
    item.classList.remove('error', 'warning');
    const existingError = item.querySelector('.property-error, .property-warning');
    if (existingError) {
      existingError.remove();
    }
  }

  createPinConfigSection(node) {
    const section = document.createElement('div');
    section.className = 'property-group';

    const header = document.createElement('div');
    header.className = 'property-group-header';
    header.innerHTML = `
      <span>Pin Configuration</span>
      <i class="fas fa-chevron-down toggle-icon"></i>
    `;

    const content = document.createElement('div');
    content.className = 'property-group-content';

    // Input pins
    if (node.pins.input && node.pins.input.definitions) {
      const inputSection = document.createElement('div');
      inputSection.innerHTML = '<h5>Input Pins</h5>';

      node.pins.input.definitions.forEach(pin => {
        const pinConfig = this.createPinConfigItem(pin, 'input');
        inputSection.appendChild(pinConfig);
      });

      content.appendChild(inputSection);
    }

    // Output pins
    if (node.pins.output && node.pins.output.definitions) {
      const outputSection = document.createElement('div');
      outputSection.innerHTML = '<h5>Output Pins</h5>';

      node.pins.output.definitions.forEach(pin => {
        const pinConfig = this.createPinConfigItem(pin, 'output');
        outputSection.appendChild(pinConfig);
      });

      content.appendChild(outputSection);
    }

    // Toggle functionality
    header.addEventListener('click', () => {
      section.classList.toggle('collapsed');
    });

    section.appendChild(header);
    section.appendChild(content);

    return section;
  }

  createPinConfigItem(pin, type) {
    const item = document.createElement('div');
    item.className = 'pin-config';

    const header = document.createElement('div');
    header.className = 'pin-config-header';
    header.innerHTML = `
      <span>${pin.label || pin.name}</span>
      <span class="pin-type-badge ${type}">${pin.dataType}</span>
    `;

    const content = document.createElement('div');
    content.className = 'pin-config-content';

    if (pin.description) {
      const description = document.createElement('div');
      description.textContent = pin.description;
      description.style.fontSize = '11px';
      description.style.color = 'var(--text-secondary)';
      content.appendChild(description);
    }

    item.appendChild(header);
    item.appendChild(content);

    return item;
  }

  createAdvancedSection(node) {
    const section = document.createElement('div');
    section.className = 'advanced-section';

    const toggle = document.createElement('button');
    toggle.className = 'advanced-toggle';
    toggle.innerHTML = `
      <i class="fas fa-cog"></i>
      <span>Advanced Settings</span>
    `;

    const content = document.createElement('div');
    content.className = 'advanced-content';

    // Node ID (read-only)
    const idGroup = document.createElement('div');
    idGroup.className = 'property-item';
    idGroup.innerHTML = `
      <label class="property-label">Node ID</label>
      <input type="text" class="property-input" value="${node.id}" readonly>
    `;

    // Node Type (read-only)
    const typeGroup = document.createElement('div');
    typeGroup.className = 'property-item';
    typeGroup.innerHTML = `
      <label class="property-label">Node Type</label>
      <input type="text" class="property-input" value="${node.type}" readonly>
    `;

    // Position
    const positionGroup = document.createElement('div');
    positionGroup.className = 'property-item';
    positionGroup.innerHTML = `
      <label class="property-label">Position</label>
      <div style="display: flex; gap: 8px;">
        <input type="number" class="property-input" placeholder="X" value="${node.position.x}" readonly>
        <input type="number" class="property-input" placeholder="Y" value="${node.position.y}" readonly>
      </div>
    `;

    content.appendChild(idGroup);
    content.appendChild(typeGroup);
    content.appendChild(positionGroup);

    // Toggle functionality
    toggle.addEventListener('click', () => {
      content.classList.toggle('visible');
      const icon = toggle.querySelector('i');
      icon.classList.toggle('fa-cog');
      icon.classList.toggle('fa-cog-spin');
    });

    section.appendChild(toggle);
    section.appendChild(content);

    return section;
  }

  showMultiSelection(selectedNodes) {
    this.element.innerHTML = '';

    const panel = document.createElement('div');
    panel.className = 'multi-selection-panel';

    const count = document.createElement('div');
    count.className = 'multi-selection-count';
    count.textContent = selectedNodes.length;

    const label = document.createElement('div');
    label.textContent = 'nodes selected';

    const actions = document.createElement('div');
    actions.className = 'multi-selection-actions';

    // Common actions for multiple nodes
    const deleteButton = document.createElement('button');
    deleteButton.className = 'property-button';
    deleteButton.innerHTML = '<i class="fas fa-trash"></i> Delete All';
    deleteButton.addEventListener('click', () => {
      this.eventEmitter.emit('nodes:delete:multiple', selectedNodes);
    });

    const duplicateButton = document.createElement('button');
    duplicateButton.className = 'property-button';
    duplicateButton.innerHTML = '<i class="fas fa-copy"></i> Duplicate All';
    duplicateButton.addEventListener('click', () => {
      this.eventEmitter.emit('nodes:duplicate:multiple', selectedNodes);
    });

    actions.appendChild(duplicateButton);
    actions.appendChild(deleteButton);

    panel.appendChild(count);
    panel.appendChild(label);
    panel.appendChild(actions);

    this.element.appendChild(panel);
  }

  hide() {
    this.element.innerHTML = `
      <div class="no-selection">
        <i class="fas fa-mouse-pointer"></i>
        <p>Select a node to view its properties</p>
      </div>
    `;

    this.currentNode = null;
    this.currentConfig = {};
    this.formElements.clear();
    this.validators.clear();
  }

  validateAllProperties() {
    const errors = new Map();

    this.validators.forEach((validator, propertyName) => {
      const input = this.formElements.get(propertyName);
      if (input) {
        const value = this.getInputValue(input);
        const propertyErrors = validator(value);
        if (propertyErrors.length > 0) {
          errors.set(propertyName, propertyErrors);
        }
      }
    });

    return errors;
  }

  getCurrentConfig() {
    return { ...this.currentConfig };
  }

  setConfig(config) {
    this.currentConfig = { ...config };
    this.updateFormValues();
  }

  refresh() {
    if (this.currentNode) {
      this.renderNodeProperties(this.currentNode);
    }
  }
}
